apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fe-ev-csms-api-ingress
  namespace: "${NAMESPACE}"
  labels:
    app: fe-ev-csms-api-ingress
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing # must be internet-facing since Driiv<PERSON> is in another network
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/healthcheck-path: /health
    prometheus.io/path: /metrics
    prometheus.io/port: "9090"
    prometheus.io/scrape: "true"
spec:
  ingressClassName: alb
  rules:
  - host: ${host}
    http:
      paths:
      # fe-ev-csms-api-us-elv
      - path: /ocpi/us/elv/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: fe-ev-csms-api-us-elv
            port:
              number: ${SERVER_PORT}
      - path: /ocpi/emsp/locations/*/us/elv/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: fe-ev-csms-api-us-elv
            port:
              number: ${SERVER_PORT}
      # fe-ev-csms-api-us-dom
      - path: /ocpi/us/dom/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: fe-ev-csms-api-us-dom
            port:
              number: ${SERVER_PORT}
      - path: /ocpi/emsp/locations/*/us/dom/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: fe-ev-csms-api-us-dom
            port:
              number: ${SERVER_PORT}
      # fe-ev-csms-api-us-sit
      # uncomment once deployed
      # - path: /ocpi/us/sit/*
      #   pathType: ImplementationSpecific
      #   backend:
      #     service:
      #       name: fe-ev-ocpi-us-sit
      #       port:
      #         number: ${SERVER_PORT}
