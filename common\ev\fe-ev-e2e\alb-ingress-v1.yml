apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fe-ev-e2e-ingress
  namespace: "${NAMESPACE}"
  labels:
    app: fe-ev-e2e-ingress
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/healthcheck-path: /health
    prometheus.io/path: /metrics
    prometheus.io/port: "9090"
    prometheus.io/scrape: "true"
spec:
  ingressClassName: alb
  rules:
  - host: ${host}
    http:
      paths:
      - path: /receipt/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: fe-ev-e2e
            port:
              number: ${SERVER_PORT}