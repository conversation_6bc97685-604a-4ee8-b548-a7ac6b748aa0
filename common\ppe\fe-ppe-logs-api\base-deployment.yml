---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-ppe-logs-api
  name: fe-ppe-logs-api
  namespace: fe-ppe
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-ppe-logs-api
  template:
    metadata:
      labels:
        app: fe-ppe-logs-api
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-ppe-logs-api
          image: ${image_repo}/fe-ppe-logs-api:${image_tag}
          ports:
            - containerPort: 8080
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: USE_PRETTY_LOGS
              value: "${USE_PRETTY_LOGS}"

            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: LOOKUP_API_HOST
              value: "${LOOKUP_API_HOST}"
            - name: CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ppe-secrets
                  key: keycloak-client-secret
            - name: CLIENT_ID
              value: "${CLIENT_ID}"
            - name: SERVICE_USERNAME
              value: "${SERVICE_USERNAME}"
            - name: SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ppe-secrets
                  key: fe-ppe-logs-api-hubacc-password

            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: READ_HEADER_TIMEOUT
              value: "${READ_HEADER_TIMEOUT}"

            - name: UCC_CMD_API_URL
              value: "${UCC_CMD_API_URL}"
            - name: IA_SITE_API_URL
              value: "${IA_SITE_API_URL}"
            - name: UCC_PROPS_API_URL
              value: "${UCC_PROPS_API_URL}"
            - name: IA_NOTIF_API
              value: "${IA_NOTIF_API}"
            - name: S3_REGION
              value: "${S3_REGION}"
            - name: S3_FILE_DURATION_IN_SECONDS
              value: "${S3_FILE_DURATION_IN_SECONDS}"
            - name: S3_USE_RESOLVER
              value: "${S3_USE_RESOLVER}"

            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_LOG_REQUEST_COLL
              value: "${MONGO_LOG_REQUEST_COLL}"
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"
            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"

            - name: RTD_ENABLED
              value: "${RTD_ENABLED}"
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECTS
              value: "${RTD_SUBJECTS}"
            - name: RTD_CONSUMER_NAME
              value: "${RTD_CONSUMER_NAME}"
            - name: RTD_CONSUMER_DURABLE
              value: "${RTD_CONSUMER_DURABLE}"
            - name: NATS_WORKER_COUNT
              value: "${NATS_WORKER_COUNT}"
            - name: NATS_NKEY_ENABLED
              value: "${NATS_NKEY_ENABLED}"
            - name: NATS_NKEY_PUBLIC
              value: "${NATS_NKEY_PUBLIC}"
            - name: NATS_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ppe-secrets
                  key: fe-ppe-logs-api-nats-key-private