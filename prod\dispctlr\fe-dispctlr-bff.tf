resource "kubernetes_manifest" "fe_dispctlr_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/dispctlr/fe-dispctlr-bff/base-deployment.yml", {
    name      = "fe-dispctlr-bff"
    NAMESPACE = local.namespace
    REPLICAS   = 0

    image_repo = local.image_repo
    image_tag  = local.fe-dispctlr-bff.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL      = "info"
    CONTAINER_PORT = local.server_port
    SERVER_PORT    = format(":%s", tostring(local.server_port))

    KC_HOST        = local.kc_host_url
    KC_REALM       = local.kc_realm

  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_dispctlr_bff" {
  metadata {
    name      = "fe-dispctlr-bff"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-dispctlr-bff"
    }
    port {
      port        = 8080
      target_port = 8080
    }

    type = "ClusterIP"
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_dispctlr_bff_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-dispctlr-bff"
    NAMESPACE   = local.namespace
    TARGET_PORT = local.server_port
  }))
}