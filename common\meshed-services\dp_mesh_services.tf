locals {
  dp_server_port = 8080
}

resource "kubernetes_service" "dp_mirror_api_v1" {
  metadata {
    name      = "dp-mirror-api-v1"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "dp-mirror-api-v1"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_topo_api_v1" {
  metadata {
    name      = "dp-topo-api-v1"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "dp-topo-api-v1"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_gvr1_cdc_api" {
  metadata {
    name      = "dp-gvr1-cdc-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-gvr1-cdc-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_inventory_api" {
  metadata {
    name      = "dp-wsm-inventory-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-inventory-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_sale_api" {
  metadata {
    name      = "dp-wsm-sale-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-sale-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_mhr_recon_api" {
  metadata {
    name      = "dp-wsm-mhr-recon-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-mhr-recon-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_daily_sale_api" {
  metadata {
    name      = "dp-wsm-daily-sale-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-daily-sale-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_fpm_status_api" {
  metadata {
    name      = "dp-wsm-fpm-status-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-fpm-status-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_tank_status_api" {
  metadata {
    name      = "dp-wsm-tank-status-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-tank-status-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_meter_var_api" {
  metadata {
    name      = "dp-wsm-meter-var-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-meter-var-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_ucc_props_api" {
  metadata {
    name      = "dp-ucc-props-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "dp-ucc-props-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_ucc_telem_api" {
  metadata {
    name      = "dp-ucc-telem-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "dp-ucc-telem-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "nats" {
  metadata {
    name      = "nats"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      "app.kubernetes.io/component" = "nats"
      "app.kubernetes.io/instance"  = "nats"
      "app.kubernetes.io/name"      = "nats"
    }
    port {
      name        = "nats"
      port        = 4222
      target_port = "nats"
    }

    port {
      name        = "gateway"
      port        = 7222
      target_port = "gateway"
    }
  }
}

resource "kubernetes_service" "dp_wsm_meter_flow_api" {
  metadata {
    name      = "dp-wsm-meter-flow-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-meter-flow-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

resource "kubernetes_service" "dp_wsm_dispenser_status_api" {
  metadata {
    name      = "dp-wsm-dispenser-status-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "dp-wsm-dispenser-status-api"
    }
    port {
      port        = local.dp_server_port
      target_port = local.dp_server_port
    }
  }
}

moved {
  from = kubernetes_service.dp-gvr1-cdc-api
  to   = kubernetes_service.dp_gvr1_cdc_api
}

moved {
  from = kubernetes_service.dp-wsm-inventory-api
  to   = kubernetes_service.dp_wsm_inventory_api
}

moved {
  from = kubernetes_service.dp-wsm-sale-api
  to   = kubernetes_service.dp_wsm_sale_api
}

moved {
  from = kubernetes_service.dp-wsm-mhr-recon-api
  to   = kubernetes_service.dp_wsm_mhr_recon_api
}

moved {
  from = kubernetes_service.dp-wsm-daily-sale-api
  to   = kubernetes_service.dp_wsm_daily_sale_api
}
moved {
  from = kubernetes_service.dp-wsm-fpm-status-api
  to   = kubernetes_service.dp_wsm_fpm_status_api
}

moved {
  from = kubernetes_service.dp-wsm-tank-status-api
  to   = kubernetes_service.dp_wsm_tank_status_api
}

moved {
  from = kubernetes_service.dp-wsm-meter-flow-api
  to   = kubernetes_service.dp_wsm_meter_flow_api
}


moved {
  from = kubernetes_service.dp-wsm-dispenser-status-api
  to   = kubernetes_service.dp_wsm_dispenser_status_api
}