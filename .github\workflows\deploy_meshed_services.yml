name: Update Meshed Service
on:
  push:
    tags:
      - "release/meshed-services/*/v*"

jobs:
  deploy-dev:
    name: Dev Deploy Meshed Services
    if: contains(github.ref, 'release/meshed-services/dev')
    runs-on: [dev-fe]
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2
      - name: Execute Terraform
        id: terraform
        run: |
          cd dev/meshed-services
          terraform init
          terraform apply --auto-approve

  deploy-prod:
    name: Prod Deploy Meshed Services
    if: contains(github.ref, 'release/meshed-services/prod')
    runs-on: [prod-fe-fe-deploy]
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2
      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/meshed-services
          terraform init
          terraform apply --auto-approve