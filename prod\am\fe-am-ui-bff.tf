variable "device_classes" {
  default = <<EOF
[{\"id\":\"connectivityBoard\",\"label\":\"Connectivity Board\",\"deviceTypes\":[\"OMNIA\",\"SSOM\"]},{\"id\":\"paymentTerminal\",\"label\":\"Payment Terminal\",\"deviceTypes\":[\"M7\",\"M5\"]},{\"id\":\"pcn\",\"label\":\"Pump Control Node\",\"deviceTypes\":[\"PCN\"]}]
EOF
}
variable "default_global_rule" {
  default = <<EOF
{\"description\": \"Calculates site status based on configured threshold\", \"name\": \"global forecourt site operational status rule\", \"orgIds\": [], \"parametersMap\": {\"deviceClasses\": {\"operator\": \"in\", \"value\": [\"connectivityBoard\", \"pcn\", \"paymentTerminal\"]}, \"operationalThreshold\": {\"operator\": \"=\", \"value\": 80}, \"warningThreshold\": {\"operator\": \"=\", \"value\": 50}}, \"periodicity\": 300, \"priority\": 1, \"ruleType\": \"forecourt-site-operational-status\", \"scope\": \"global\", \"severity\": 1}
EOF
}
resource "kubernetes_manifest" "fe_am_ui_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/am/fe-am-ui-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-am-ui-bff.version
    REPLICAS   = 1

    LOG_LEVEL = "info"
    SERVER_PORT = 3000
    DP_MIRROR_API_URL = "http://dp-mirror-api-v1.dp.svc.cluster.local:8080"
    DP_TOPO_API_URL = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"
    IM_ALERTRULES_API_URL = "http://im-alertrules-api-v2.im.svc.cluster.local:8080"
    KC_CERT_URL = "https://auth.hub.invenco.com/auth/realms/insite360-users/protocol/openid-connect/certs"
    DEVICE_CLASSES = var.device_classes
    IA_API_URI = "http://ia-api-v2.ia.svc.cluster.local:8888"
    FE_AM_UI_FILTERS_API_URL = "http://fe-am-ui-filters-api.fe-am.svc.cluster.local:8080"
    IM_ALERTS_API_URL = "http://im-alert-api.im.svc.cluster.local:8080"
    DEFAULT_GLOBAL_RULE=var.default_global_rule
    DEFAULT_PERIODICITY=60
    AUTH_DOMAIN="http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    AUTH_REALM="invenco-hub"

    CPU_REQUEST    = "10m"
    MEM_REQUEST    = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_am_ui_bff" {
  metadata {
    name      = "fe-am-ui-bff"
    namespace = "fe-am"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-am-ui-bff"
    }
    port {
      port        = 3000
      target_port = 3000
    }

    type = "NodePort"
  }
}