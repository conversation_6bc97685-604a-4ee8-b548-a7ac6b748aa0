---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fe-am-ui-devices
  namespace: fe-am
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-am-ui-devices
  template:
    metadata:
      labels:
        app: fe-am-ui-devices
    spec:
      containers:
        - name: fe-am-ui-devices
          image: ${image_repo}/fe-am-ui-devices:${image_tag}
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
          env:
            - name: FE_AM_UI_BFF
              value: "fe-am-ui-bff"
