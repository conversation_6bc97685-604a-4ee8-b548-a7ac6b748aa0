locals {
  cp_server_port = 8081
}

resource "kubernetes_service" "cp_cmd_api" {
  metadata {
    name      = "cp-cmd-api"
    namespace = "cp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "cp-cmd-api"
    }
    port {
      port        = local.cp_server_port
      target_port = local.cp_server_port
    }
  }
}

resource "kubernetes_service" "cp_ucc_cmd_api" {
  metadata {
    name      = "cp-ucc-cmd-api"
    namespace = "cp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "cp-ucc-cmd-api"
    }
    port {
      port        = 8080
      target_port = 8080
    }
  }
}

resource "kubernetes_service" "cp_nats" {
  metadata {
    name      = "nats"
    namespace = "cp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      "app.kubernetes.io/component" = "nats"
      "app.kubernetes.io/instance"  = "nats"
      "app.kubernetes.io/name"      = "nats"
    }

    port {
      name        = "gateway"
      port        = 7222
      target_port = "gateway"
    }
  }
}

resource "kubernetes_service" "cp_ppe_deployments_api" {
  metadata {
    name      = "cp-ppe-deployments-api"
    namespace = "cp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "cp-ppe-deployments-api"
    }
    port {
      port        = 8080
      target_port = 8080
    }
  }
}

