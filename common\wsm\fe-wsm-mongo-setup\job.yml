apiVersion: batch/v1
kind: Job
metadata:
  name: ${name}
  namespace: ${namespace}
spec:
  ttlSecondsAfterFinished: 60
  template:
    metadata:
      name: ${name}
      namespace: ${namespace}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - image: ${image_repo}/${name}:${image_tag}
          name: ${name}
          volumeMounts:
            - name: config
              mountPath: /collections
              readOnly: true
          env:
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: M<PERSON><PERSON><PERSON>_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
      restartPolicy: Never

      volumes:
        - name: config
          configMap:
            name: fe-wsm-mongo-setup-config