---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-wm-bff
  name: fe-wm-bff
  namespace: fe-wm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-wm-bff
  template:
    metadata:
      labels:
        app: fe-wm-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-wm-bff
          image: ${image_repo}/fe-wm-bff:${image_tag}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: DP_MIRROR_API_URL
              value: "${DP_MIRROR_API_URL}"
            - name: DP_MIRROR_API_CLIENT_TIMEOUT_MS
              value: "10000"
            - name: DP_TOPO_API_URL
              value: "${DP_TOPO_API_URL}"
            - name: DP_TOPO_API_CLIENT_TIMEOUT_MS
              value: "10000"
            - name: NOTIFICATION_API_URL
              value: "${IA_NOTIFICATION_API_URL}"
            - name: NOTIFICATION_API_CLIENT_TIMEOUT_MS
              value: "10000"
            - name: REDIS_URL
              value: "${REDIS_URL}"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: password
            - name: DIGIST_FOR_OTP_VERIFICATION
              value: "6"
            - name: MINUTES_TTL_OTP
              value: "5"
            - name: MINUTES_TTL_SESSION_TOKEN
              value: ${MINUTES_TTL_SESSION_TOKEN}
            - name: AUTH_DOMAIN
              value: ${AUTH_DOMAIN}
            - name: AUTH_REALM
              value: ${AUTH_REALM}
            - name: APP_DOMAIN
              value: ${APP_DOMAIN}
            - name: IA_CLIENT_ID
              value: ${IA_CLIENT_ID}
            - name: IA_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-wm-secrets
                  key: fe-wm-bff-ia-secret
            - name: IA_SERVICE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: fe-wm-secrets
                  key: fe-wm-bff-ia-user
            - name: IA_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-wm-secrets
                  key: fe-wm-bff-ia-password
            - name: CP_CMD_URL
              value: ${CP_CMD_URL}
            - name: CP_CMD_CLIENT
              value: ${CP_CMD_CLIENT}
            - name: CP_CMD_USER_ID
              value: ${CP_CMD_USER_ID}
            - name: DIGITS_FOR_OTP_VERIFICATION
              value: ${DIGITS_FOR_OTP_VERIFICATION}
            - name: SESSION_EMAIL_FROM
              value: ${SESSION_EMAIL_FROM}
            - name: AUDIT_REPORT_PROCESS_INTERVAL_MS
              value: ${AUDIT_REPORT_PROCESS_INTERVAL_MS}
            - name: AUDIT_REPORT_PROCESS_TIMEOUT_MS
              value: ${AUDIT_REPORT_PROCESS_TIMEOUT_MS}
            - name: AUDIT_REPORT_EMAIL_FROM
              value: ${AUDIT_REPORT_EMAIL_FROM}