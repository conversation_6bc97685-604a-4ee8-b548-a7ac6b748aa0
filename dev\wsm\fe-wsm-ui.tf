resource "kubernetes_manifest" "fe_wsm_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-ui/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-wsm-ui.version
    REPLICAS   = 1

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_wsm_ui" {
  metadata {
    name      = "fe-wsm-ui"
    namespace = "fe-wsm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wsm-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}