---
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: ${kind} 
metadata:
  name: redis
  namespace: fe-wm
spec:
%{ if clusterSize != "" }
  clusterSize: ${clusterSize}
%{ endif }
  kubernetesConfig:
    image: 'quay.io/opstree/redis:v7.0.5'
    imagePullPolicy: IfNotPresent
    redisSecret:
      name: redis-secret
      key: password
  redisExporter:
    enabled: true
    image: quay.io/opstree/redis-exporter:v1.44.0
    imagePullPolicy: IfNotPresent 
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 100m
        memory: 128Mi
  storage:
    volumeClaimTemplate:
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 1Gi
  podSecurityContext:
    runAsUser: 1000
    fsGroup: 1000
