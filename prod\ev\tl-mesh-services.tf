resource "kubernetes_namespace" "tl" {
  metadata {
    name = "tl"
    annotations = {
      name = "tl"
    }
  }
}

resource "kubernetes_service" "tl_temporal_frontend" {
  metadata {
    name      = "tl-temporal-frontend"
    namespace = "tl"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }

  spec {
    selector = {
      "app.kubernetes.io/component" : "frontend"
      "app.kubernetes.io/instance" : "tl-temporal"
      "app.kubernetes.io/name" : "temporal"
    }
    port {
      name        = "grpc-rpc"
      port        = 7233
      protocol    = "TCP"
      target_port = 7233
    }

    port {
      name        = "http"
      port        = 7243
      protocol    = "TCP"
      target_port = 7243
    }

    type = "ClusterIP"
  }
  depends_on = [kubernetes_namespace.tl]
}