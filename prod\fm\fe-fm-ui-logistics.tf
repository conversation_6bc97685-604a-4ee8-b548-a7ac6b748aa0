resource "kubernetes_manifest" "fe_fm_ui_logistics_deployment" {
  manifest = yamldecode(templatefile("../../common/fm/fe-fm-ui-logistics/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-fm-ui-logistics.version
    REPLICAS   = 0

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_fm_ui_logistics" {
  metadata {
    name      = "fe-fm-ui-logistics"
    namespace = "fe-fm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-fm-ui-logistics"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}
