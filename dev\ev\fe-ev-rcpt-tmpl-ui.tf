resource "kubernetes_manifest" "fe_ev_rcpt_tmpl_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-rcpt-tmpl-ui/base-deployment.yml", {
    name      = "fe-ev-rcpt-tmpl-ui"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-rcpt-tmpl-ui.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_rcpt_tmpl_ui" {
  metadata {
    name      = "fe-ev-rcpt-tmpl-ui"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-rcpt-tmpl-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}