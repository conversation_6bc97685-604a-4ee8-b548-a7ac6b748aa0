resource "kubernetes_manifest" "fe_tlstech_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/tlstech/fe-tlstech-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-tlstech-bff.version
    REPLICAS   = 1

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL   = "info"
    SERVER_PORT = local.server_port

    KC_HOST        = local.kc_host_url
    KC_REALM       = local.kc_realm

    DEV_BOOTSTRAP_API_HOST = local.ia_devbootstrap_api_url
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_tlstech_bff" {
  metadata {
    name      = "fe-tlstech-bff"
    namespace = "fe-tlstech"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-tlstech-bff"
    }
    port {
      port        = 8080
      target_port = 8080
    }

    type = "NodePort"
  }
}
