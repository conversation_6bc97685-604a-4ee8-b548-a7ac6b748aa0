resource "kubernetes_manifest" "fe_ev_csms_io_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-io/base-deployment.yml", {
    name      = "fe-ev-csms-io"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-csms-io.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL           = "info"
    SERVER_READ_TIMEOUT = local.server_timeout
    METRICS_SERVER_PORT         = local.metrics_server_port

    KC_HOST               = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    KC_REALM              = "invenco-hub"
    AUTH_CLIENT_ID        = "invenco-client"
    AUTH_SERVICE_USERNAME = "<EMAIL>"

    HTTP_UCC_HOST       = "http://cp-ucc-cmd-api.cp.svc.cluster.local:8080"
    HTTP_SESSION_HOST   = local.session_url
    HTTP_EVSE_HOST      = local.opt_evse_url
    HTTP_TARIFF_HOST    = local.tariff_url
    HTTP_LOCATION_HOST  = local.location_url
    HTTP_CDR_HOST       = local.cdr_url
    NO_OF_DAYS          = 7

    MONGO_HOST                  = local.mongo_hostname_secure
    MONGO_PORT                  = local.mongo_port
    MONGO_CREDENTIALS           = local.mongo_credentials_csms_io
    MONGO_AUTH_SOURCE           = local.mongo_auth_source
    MONGO_DATABASE              = local.mongo_database_secure
    MONGO_CONNECTION_COLLECTION = local.mongo_connection_collection
    MONGO_OPTIONS               = local.mongo_options_secure
    MONGO_SCHEMA                = local.mongo_schema

    NATS_URL     = local.rtd_url
    NATS_SUBJECTS = join(",", local.csms_io_filters) 
    HTTP_RETRY_MAX_INTERVAL =  "10s"
    NATS_STREAM = "dp-ucc-telem-in"
    NATS_AUTH_ENABLED = false

  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_csms_io_service" {
  metadata {
    name      = "fe-ev-csms-io"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-csms-io"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}


#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_csms_io_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-io/vds.yml", {
    MONGO_ROLE = "fe-ev-csms-io"
    name       = "fe-ev-csms-io"
    env        = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_csms_io_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-csms-io"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = 9090
  }))
}
