---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/fe-ev-cdr-in:${image_tag}
          ports:
            - containerPort: ${METRICS_SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: METRICS_SERVER_PORT
              value: "${METRICS_SERVER_PORT}"

            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"
            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"
            - name: MONGO_QUERY_LIMIT
              value: "${MONGO_QUERY_LIMIT}"
            
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_CDR_COLLECTION
              value: "${MONGO_CDR_COLLECTION}"

            - name: SERVER_TIME_OUT
              value: "${SERVER_TIME_OUT}"
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTER
              value: "${RTD_SUBJECT_FILTER}"
            - name: RTD_AUTH_ENABLED
              value: "${RTD_AUTH_ENABLED}"
            - name: RTD_NKEY_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-cdr-in-nats-nkey-pub
                  optional: true
            - name: RTD_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-cdr-in-nats-nkey
                  optional: true
