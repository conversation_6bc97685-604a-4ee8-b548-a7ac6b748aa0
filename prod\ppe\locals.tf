locals {
  fe-ppe-version = "v0.2.5"

  fe-ppe-deployments-ui = {
    "version" : "v0.4.1"
  }

  fe-ppe-status-bff = {
    "version" : "v0.3.0"
  }

  fe-ppe-logs-api = {
    "version" : "v1.0.2"
  }

  namespace   = "fe-ppe"
  server_port = 8080

  image_repo      = "633377509572.dkr.ecr.us-east-1.amazonaws.com"
  ui_server_port  = 80
  bff_server_port = 8080
  podmonitor_port = 9090

  http_client_timeout   = "30s"
  http_client_retry_max = "3"

  keycloak_host          = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_invenco_realm = "invenco-hub"
  invenco_client_id      = "invenco-client"
  ia_lookup_api_url      = "ia-lookup-api.ia.svc.cluster.local:8080"

  mongo_hostname = "ppe-pl-0.pzj68g.mongodb.net"
  mongo_schema   = "mongodb+srv"


  server_read_timeout = "500s"

  rsu_api_url                = "http://remote-software-update.prod.aws.gilbarco.com:8080/remote-software-update/api"
  fe_ppe_logs_api_url        = "http://fe-ppe-logs-api.fe.svc.cluster.local:8080"
  cp_ppe_deployments_api_url = "http://cp-ppe-deployments-api.cp.svc.cluster.local:8080"
  ia_site_api_url            = "http://ia-site-api.ia.svc.cluster.local:8080"
  cp_ucc_cmd_api_url         = "http://cp-ucc-cmd-api.cp.svc.cluster.local:8080"
  dp_ucc_props_api_url       = "http://dp-ucc-props-api.dp.svc.cluster.local:8080"
  ia_notif_api_url           = "http://ia-notif-api.ia.svc.cluster.local:8080"
}
