# fe-deploy
Front End Application Deployment Repository

Applications typically consist of 2 or more microservices as follows:

* 1 `fe-<app_short_name>-ui-bff` repository that contains the [Backend for Frontend](https://bff-patterns.com/) for the Application
* 1 or more `fe-<app_short_name>-ui-<mfe_name>` repository that contains the [Micro Front Ends](https://micro-frontends.org/)

## Primary Workflow

### Are we Ready to Release your Application? 

```mermaid
---
title: Prepare for Deployment
---
flowchart TD
    A[Need to deploy through to the prod env?]
    A -- Yes --> B[Full unit and integration tests complete?]
    A -- No --> C[You are in the wrong place]
    C -- But I Only Want to Deploy to Dev Right Now! --> C2[See: https://confluence.gilbarco.com/display/EngHbook/Core+Code+Flow ]
    B -- No --> D[Ensure Automated Microservice-level Tests are Complete] --> B
    B -- Yes --> F[release_tag.yml pipeline complete for each microservice?]
    F -- Yes --> G[You are ready to attempt deployment]

```
---

### Note on `-rc#` in release tags

You may want to use a "Release Candidate" with your microservice tags if you are unsure if they will pass end to end tests.

A good practice is to append `-rc1` to the microservice tag to denote that it is a release candidate. For example, if my new bugfix release is `v0.3.0`, I might tag the first version that passes unit and integration tests as `v0.3.1-rc1` and then follow the below workflow until either:

1) I have a version of my microservice(s) that are ready for production and I retag that/those commit(s) WITHOUT the `-rc#` to prepare to deploy through to prod.
2) My microservice can not pass tests in a lower environment for an unforeseen reason and I back out of the workflow, returning the repo and lower environments to their original state. 

---

### Update IaC (manifests, k8 resources, etc)

```mermaid
---
title: Getting ready to make changes
---
flowchart TD
    A[Is there a stage/app_short_name/vX.Y.Z branch active?]
    A -- Yes --> B[Find out who is doing a deployment and wait until they are done] --> A
    A -- No --> C[Determine new subsystem version number -- see below]
    C --> D[Tell your coleagues you are preparing to deploy your new version number in the designated chat]
    D --> E[Create a new branch per your new version number]

```

> Finding the next subsystem version number
>
> 1) Find the last [tag]("./tags"). It should look like `release/<app_short_name>/prod/vX.Y.Z`
> 2) Increment that tag based on [semantic versioning](https://confluence.gilbarco.com/display/EngHbook/Semantic+Versioning).. this is the version you will use
> 3) If you are unsure, contact the dev lead/po for the subsystem

#### Create your branch

```mermaid
---
title: Create Branch from main and begin
---
gitGraph
    commit id: "someFeature"
    commit id: "someBugfix"
    branch "stage/app_short_name/v1.2.3"
    commit id: "try1myFeature" tag: "release/app_short_name/dev/v1.2.3-rc1"
```

#### Conduct the deployment workflow

```mermaid
---
title: Validating in Lower Environments
---
flowchart TD  
    A[Checkout your new branch]
    A --> B[Commit your changes, local.tf files, perhaps k8 deployments or services]
    B --> C[Tag with release/app_short_name/dev/v1.2.3-rc# -- where # is next aval rc]
    C --> D[Deployment Successfull?] -- no --> W
    D -- yes --> E[E2E Tests Passing?]
    W[Giving up?] -- no --> X[Update microservice and push new -rc# verions and/or update IaC files] --> B
    W -- yes --> Y[Re-execute last successful github action workflow]
    Y --> Z[Report your issues to your team so they can help!]
    E -- no --> W
    E -- yes --> F[Everything look right?] -- no --> W
    F -- yes --> H[Tag microservice w/o -rc#, update local.tf, and tag xx-deploy with release/app_short_name/dev/v1.2.3]
    H --> I[For each environment through prod] --> J[tag with release/xxx/v1.2.3]
    J --> K[Merge stage/v1.2.3 with main] --> L[Report your success to your team!]
```

```mermaid
---
title: Code Deployed 
---
gitGraph
    commit id: "someFeature"
    commit id: "someBugfix"
    branch stage/app_short_name/v1.2.3
    commit id: "try1myFeature" tag: "release/app_short_name/dev/v1.2.3-rc1"
    commit id: "try2MyFeature" tag: "release/app_short_name/dev/v1.2.3-rc2"
    commit id: "goodMyFeature" tag: "release/app_short_name/dev/v1.2.3"
    commit id: "goodMyFeature" tag: "release/app_short_name/prod/v1.2.3"
    checkout main
    merge stage/app_short_name/v1.2.3 id: "myFeature"
```