---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fe-wm-ui
  namespace: fe-wm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-wm-ui
  template:
    metadata:
      labels:
        app: fe-wm-ui
    spec:
      containers:
        - name: fe-wm-ui
          image: ${image_repo}/fe-wm-ui:${image_tag}
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST} 
          env:
            - name: WM_BFF_URL
              value: "fe-wm-bff"
            - name: RESEND_CODE_TIMER
              value: "60"
            - name: SELECTION_LIMIT
              value: "5"
            - name: MINUTES_EXPIRY_SESSION_TOKEN
              value: ${MINUTES_EXPIRY_SESSION_TOKEN}
            - name: COOKIE_BANNER_TENANT_ID
              value: ${COOKIE_BANNER_TENANT_ID}
            - name: COOKIE_BANNER_DOMAIN_ID
              value: ${COOKIE_BANNER_DOMAIN_ID}
            - name: CONTACT_TEXT
              value: ${CONTACT_TEXT}
            - name: CONTACT_NUMBER
              value: ${CONTACT_NUMBER}
            - name: MILLI_SECS_REFRESH_INTERVAL
              value: ${MILLI_SECS_REFRESH_INTERVAL}
            - name: ENABLE_RECAPTCHA
              value: ${ENABLE_RECAPTCHA}
            - name: RECAPTCHA_KEY
              value: ${RECAPTCHA_KEY}

