---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${name}
  namespace: fe-ppe
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      containers:
        - name: ${name}
          image: ${image_repo}/${name}:${image_tag}
          ports:
            - containerPort: 80
          env:
            - name: FE_PPE_DEPLOYMENTS_UI_BFF
              value: "fe-ppe-status-bff"