# fe-ev-e2e-twk deployment
resource "kubernetes_manifest" "fe_ev_e2e_twk_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-e2e-twk/base-deployment.yml", {
    name      = "fe-ev-e2e-twk"
    NAMESPACE = local.namespace
    // Do not change until MSP is set in PROD
    REPLICAS  = 0

    image_repo = local.image_repo
    image_tag  = local.fe-ev-e2e-twk.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    SERVER_PORT = local.metrics_server_port
    CRON_SCHEDULE = "0 * * * *"
    TEMPORAL_HOST = local.temporal_host_port
    TEMPORAL_NAMESPACE = local.fe_ev_e2e_temporal_namespace

    HUB_AUTH_HOST = local.keycloak_host
    HUB_AUTH_REALM = local.keycloak_invenco_realm
    HUB_AUTH_CLIENT_ID = local.invenco_client_id
    HUB_AUTH_USERNAME = "<EMAIL>"

    FE_EV_CMS_API_URL = local.fe_ev_csms_api
    CP_UCC_CMD_API_URL = local.ucc_cmd_url

    LOG_LEVEL = "debug"
  }))
  field_manager {
    force_conflicts = true
  }
}


resource "kubernetes_manifest" "fe_ev_e2e_twk_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-e2e-twk"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = 9090
  }))
}
