resource "kubernetes_manifest" "fe_am_ui_sites_deployment" {
  manifest = yamldecode(templatefile("../../common/am/fe-am-ui-sites/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-am-ui-sites.version
    REPLICAS   = 1

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_am_ui_sites" {
  metadata {
    name      = "fe-am-ui-sites"
    namespace = "fe-am"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-am-ui-sites"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}
