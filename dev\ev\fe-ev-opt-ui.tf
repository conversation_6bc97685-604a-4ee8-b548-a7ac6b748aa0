resource "kubernetes_manifest" "fe_ev_opt_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-opt-ui/base-deployment.yml", {
    name      = "fe-ev-opt-ui"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-opt-ui.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_opt_ui" {
  metadata {
    name      = "fe-ev-opt-ui"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-opt-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}