# fe-ev-location-api deployment
resource "kubernetes_manifest" "fe_ev_location_api_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-location-api/base-deployment.yml", {
    name      = "fe-ev-location-api"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-location-api.version

    LOG_LEVEL       = "info"
    SERVER_PORT     = local.server_port
    SERVER_TIME_OUT = local.server_timeout

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE            = local.mongo_database_common
    MONGO_LOCATION_COLLECTION = local.mongo_location_collection
  }))
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-location-api service
resource "kubernetes_service" "fe_ev_location_api_service" {
  metadata {
    name      = "fe-ev-location-api"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app   = "fe-ev-location-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_location_api_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-location-api/vds.yml", {
    MONGO_ROLE = "fe-ev-location-api"
    name       = "fe-ev-location-api"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_location_api_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-location-api"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
