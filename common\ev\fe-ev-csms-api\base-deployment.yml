---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
    cc: ${COUNTRY_CODE}
    party: ${PARTY_CODE}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
      cc: ${COUNTRY_CODE}
      party: ${PARTY_CODE}
  template:
    metadata:
      labels:
        app: ${name}
        cc: ${COUNTRY_CODE}
        party: ${PARTY_CODE}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/fe-ev-csms-api:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: RESTY_DEBUG_ENABLED
              value: "${RESTY_DEBUG_ENABLED}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_CONNECTION_COLLECTION
              value: "${MONGO_CONNECTION_COLLECTION}"

            - name: MONGO_HOST_COMMON
              value: "${MONGO_HOST_COMMON}"
            - name: MONGO_OPTIONS_COMMON
              value: "${MONGO_OPTIONS_COMMON}"
            - name: MONGO_DATABASE_COMMON
              value: "${MONGO_DATABASE_COMMON}"
            - name: MONGO_LOCATION_COLLECTION
              value: "${MONGO_LOCATION_COLLECTION}"

            - name: BOOTSTRAP
              value: "${BOOTSTRAP}"
            - name: SERVER_TIME_OUT
              value: "${SERVER_TIME_OUT}"
            - name: CPO_API_URL
              value: "${CPO_API_URL}"
            - name: OCPI_BASE_URL
              value: "${OCPI_BASE_URL}"
            - name: PARTY_CODE
              value: "${PARTY_CODE}"
            - name: COUNTRY_CODE
              value: "${COUNTRY_CODE}"
            - name: CREDENTIALS_TOKEN_A
              value: "${CREDENTIALS_TOKEN_A}"
            - name: AVAILABLE_VERSIONS
              value: "${AVAILABLE_VERSIONS}"
            - name: AVAILABLE_MODULES
              value: "${AVAILABLE_MODULES}"
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTER
              value: "${RTD_SUBJECT_FILTER}"
            - name: LOCATION_URL
              value: "${LOCATION_URL}"
            - name: SESSION_URL
              value: "${SESSION_URL}"
            - name: CDR_URL
              value: "${CDR_URL}"
            - name: TARIFF_URL
              value: "${TARIFF_URL}"
            - name: RTD_AUTH_ENABLED
              value: "${RTD_AUTH_ENABLED}"
            - name: RTD_NKEY_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-csms-api-nats-nkey-pub
                  optional: true
            - name: RTD_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-csms-api-nats-nkey
                  optional: true
