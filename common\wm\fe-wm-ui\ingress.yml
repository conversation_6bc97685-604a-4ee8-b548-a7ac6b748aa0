apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    prometheus.io/path: /metrics
    prometheus.io/port: "8080"
    prometheus.io/scrape: "true"
  labels:
    app: fe-wm-ui
  name: fe-wm-ui
  namespace: fe-wm
spec:
  ingressClassName: alb
  rules:
  - host: ${host} 
    http:
      paths:
      - backend:
          service:
            name: fe-wm-ui
            port:
              number: 80
        path: /*
        pathType: ImplementationSpecific
