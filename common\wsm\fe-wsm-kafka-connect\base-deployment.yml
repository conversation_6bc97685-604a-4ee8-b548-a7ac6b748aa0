---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${name}
  namespace: fe-wsm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: 5556
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        # Kafka Connect container
        - name: ${name}
          image: ${image_repo}/fe-wsm-kafka-connect:${image_tag}
          ports:
            - containerPort: 8083
              name: connector-api
              protocol: TCP
            - containerPort: 5556
              name: metrics
            - containerPort: 9092
              name: kafka
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
            limits:
              memory: "${MEMORY_LIMIT}"
          volumeMounts:
            - name: connectors-config
              mountPath: /usr/share/confluent-hub-components/connectors
          command:
            - /bin/bash
            - -c
            - |
              echo 'Starting Kafka Connect...'
              /etc/confluent/docker/run &
        
              echo 'Waiting 60 seconds for Kafka Connect to start...'
              sleep 60
        
              echo 'Running startup script...'
              ${CUSTOM_SCRIPT_PATH}
        
              echo 'Script completed. Keeping container alive...'
              sleep infinity
          env:
            - name: SNOWFLAKE_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: fe-wsm-secrets
                  key: fe-wsm-snowflake-private-key
            - name: CONNECTORS_CONFIG_PATH
              value: "${CONNECTORS_CONFIG_PATH}"
            - name: CUSTOM_SCRIPT_PATH
              value: "${CUSTOM_SCRIPT_PATH}"
            - name: CONNECT_BOOTSTRAP_SERVERS
              value: "${CONNECT_BOOTSTRAP_SERVERS}"
            - name: CONNECT_SECURITY_PROTOCOL
              value: PLAINTEXT
            - name: CONNECT_REST_ADVERTISED_HOST_NAME
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: CONNECT_SASL_JAAS_CONFIG
              value: "${CONNECT_SASL_JAAS_CONFIG}"
            - name: KAFKA_SASL_SERVER_CALLBACK_HANDLER_CLASS
              value: "${KAFKA_SASL_SERVER_CALLBACK_HANDLER_CLASS}"
            - name: CONNECT_GROUP_ID
              value: "${CONNECT_GROUP_ID}"
            - name: CONNECT_CONFIG_STORAGE_TOPIC
              value: "${CONNECT_CONFIG_STORAGE_TOPIC}"
            - name: CONNECT_OFFSET_STORAGE_TOPIC
              value: "${CONNECT_OFFSET_STORAGE_TOPIC}"
            - name: CONNECT_STATUS_STORAGE_TOPIC
              value: "${CONNECT_STATUS_STORAGE_TOPIC}"
            - name: KAFKA_HEAP_OPTS
              value: "${KAFKA_HEAP_OPTS}"
            - name: CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR
              value: "${CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR}"
            - name: CONNECT_STATUS_STORAGE_REPLICATION_FACTOR
              value: "${CONNECT_STATUS_STORAGE_REPLICATION_FACTOR}"
            - name: CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR
              value: "${CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR}"
            - name: CONNECT_INTERNAL_KEY_CONVERTER
              value: org.apache.kafka.connect.json.JsonConverter
            - name: CONNECT_INTERNAL_VALUE_CONVERTER
              value: org.apache.kafka.connect.json.JsonConverter
            - name: CONNECT_KEY_CONVERTER
              value: org.apache.kafka.connect.json.JsonConverter
            - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
              value: "false"
            - name: CONNECT_PLUGIN_PATH
              value: /usr/share/java,/usr/share/confluent-hub-components,/data/connect-jars,/usr/share/connectors,/opt/secret-providers
            - name: CONNECT_VALUE_CONVERTER
              value: org.apache.kafka.connect.json.JsonConverter
            - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
              value: "false"
            - name: KAFKA_JMX_PORT
              value: "5555"
            - name: KAFKA_OPTS
              value: "-javaagent:/opt/jmx_prometheus_javaagent-1.0.1.jar=5556:/opt/jmx-export-agent.yml"
            - name: SNOWFLAKE_KEY_LINE_ENDING
              value: "${SNOWFLAKE_KEY_LINE_ENDING}"
      volumes:
        - name: connectors-config
          configMap:
            name: fe-wsm-kafka-connectors-config