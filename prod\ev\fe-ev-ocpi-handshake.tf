# US SIT
resource "kubernetes_manifest" "fe_ev_ocpi_handshake_us_sit_job" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-api/job.yml", {
    name      = "fe-ev-ocpi-us-sit-handshake"
    NAMESPACE = local.namespace

    image_repo = local.image_repo
    image_tag  = local.fe-ev-csms-api.version

    LOG_LEVEL       = "info"
    SERVER_PORT     = local.server_port
    SERVER_TIME_OUT = local.server_timeout

    MONGO_AUTH_SOURCE = local.mongo_auth_source
    MONGO_DATABASE    = local.mongo_database_secure
    MONGO_HOST        = local.mongo_hostname_secure
    MONGO_PORT        = local.mongo_port
    MONGO_OPTIONS     = local.mongo_options_secure
    MONGO_SCHEMA      = local.mongo_schema
    MONGO_CREDENTIALS = local.mongo_credentials_csms_api
    MONGO_CONNECTION_COLLECTION = local.mongo_connection_collection

    MONGO_HOST_COMMON         = local.mongo_hostname_common
    MONGO_OPTIONS_COMMON      = local.mongo_options_secure
    MONGO_DATABASE_COMMON     = local.mongo_database_common
    MONGO_LOCATION_COLLECTION = local.mongo_location_collection
    
    BOOTSTRAP           = true
    CPO_API_URL         = "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
    OCPI_BASE_URL       = "https://${local.fe-ev-csms-api.host}"
    PARTY_CODE          = "SIT"
    COUNTRY_CODE        = "US"
    CREDENTIALS_TOKEN_A = "7d1f59ec-e8bf-41b6-900c-464df6502690"
    AVAILABLE_VERSIONS  = join(",", local.ocpi_versions)
    AVAILABLE_MODULES   = join(",", local.ocpi_modules)

    LOCATION_URL        = local.location_url
    RTD_URL             = local.rtd_url
    RTD_STREAM          = local.rtd_stream
    RTD_CLIENT_NAME     = local.rtd_client_name
    RTD_SUBJECT_FILTER  = local.rtd_location_subject_filter

    RESTY_DEBUG_ENABLED = false
  }))

  field_manager {
    force_conflicts = true
  }
}

