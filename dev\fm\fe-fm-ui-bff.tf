resource "kubernetes_manifest" "fe_fm_ui_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/fm/fe-fm-ui-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-fm-ui-bff.version
    REPLICAS   = 1

    LOG_LEVEL = "info"
    SERVER_PORT = 3000
    DP_MIRROR_API_URL = "http://dp-mirror-api-v1.dp.svc.cluster.local:8080"
    DP_TOPO_API_URL = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"
    DP_CDC_API_URL = "http://dp-gvr1-cdc-api.dp.svc.cluster.local:8080"
    ALERT_RULES_API_URL  = "http://im-alertrules-api-v2.im.svc.cluster.local:8080"

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_fm_ui_bff" {
  metadata {
    name      = "fe-fm-ui-bff"
    namespace = "fe-fm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-fm-ui-bff"
    }
    port {
      port        = 3000
      target_port = 3000
    }

    type = "NodePort"
  }
}
