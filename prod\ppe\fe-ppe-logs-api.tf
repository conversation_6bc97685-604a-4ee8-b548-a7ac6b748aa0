resource "kubernetes_manifest" "fe_ppe_logs_api_deployment" {
  manifest = yamldecode(templatefile("../../common/ppe/fe-ppe-logs-api/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-ppe-logs-api.version

    NAMESPACE   = "fe"
    NAME        = "fe-ppe-logs-api"
    REPLICAS    = 1
    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    ## LOG
    LOG_LEVEL       = "info"
    USE_PRETTY_LOGS = "false"

    ## AUTH
    KC_HOST          = local.keycloak_host
    KC_REALM         = local.keycloak_invenco_realm
    CLIENT_ID        = local.invenco_client_id
    LOOKUP_API_HOST  = local.ia_lookup_api_url
    SERVICE_USERNAME = "<EMAIL>"

    ## SERVER
    SERVER_PORT         = ":8080"
    READ_HEADER_TIMEOUT = "10000"

    ## DB
    MONGO_CREDENTIALS      = "fe-ppe-logs-api-vds"
    MONGO_SCHEMA           = local.mongo_schema
    MONGO_HOST             = local.mongo_hostname
    MONGO_DATABASE         = "passport_enterprise"
    MONGO_AUTH_SOURCE      = "admin"
    MONGO_OPTIONS          = "retryWrites=true&w=majority"
    MONGO_MAX_POOL_SIZE    = "100"
    MONGO_TIMEOUT          = "50s"
    MONGO_LOG_REQUEST_COLL = "logs_request"

    ## APIS
    UCC_CMD_API_URL   = local.cp_ucc_cmd_api_url
    IA_SITE_API_URL   = local.ia_site_api_url
    UCC_PROPS_API_URL = local.dp_ucc_props_api_url
    IA_NOTIF_API      = local.ia_notif_api_url

    ## S3
    S3_REGION                   = "us-east-1"
    S3_FILE_DURATION_IN_SECONDS = "900"
    S3_USE_RESOLVER             = "false"

    #RTD
    RTD_ENABLED          = true
    RTD_STREAM           = "cp-ucc-cmd-in"
    RTD_URL              = "nats://nats.fe.svc.cluster.local:4222"
    RTD_CONSUMER_NAME    = "fe-ppe-logs-api"
    RTD_CONSUMER_DURABLE = "fe-ppe-logs-api"
    RTD_CLIENT_NAME      = "cp-ucc-cmd-in"
    RTD_SUBJECTS         = "cp-ucc-cmd-in.remotemanagerservice.get-logs-cmd.>"
    NATS_WORKER_COUNT    = 10
    NATS_NKEY_ENABLED    = true
    NATS_NKEY_PUBLIC     = "UDDKGZOPG3BVVZ66EVYMUBLDGVHXWPGKEQZZ7CGF5KBZBDBBLB3LUYH3"

  }))

  computed_fields = ["spec.template.metadata.annotations"]

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ppe_logs_api" {
  metadata {
    name      = "fe-ppe-logs-api"
    namespace = "dp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ppe-logs-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }

    type = "ClusterIP"
  }
}

resource "kubernetes_manifest" "fe_ppe_logs_api_vds" {
  manifest = yamldecode(templatefile("../../common/ppe/fe-ppe-logs-api/vds.yml", {
    name       = "fe-ppe-logs-api"
    MONGO_ROLE = "fe-ppe-logs-api"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_manifest" "fe_ev_cdr_api_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ppe-logs-api"
    NAMESPACE   = "fe-ppe"
    TARGET_PORT = local.server_port
  }))
}

