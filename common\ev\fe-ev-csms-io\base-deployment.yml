---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/${name}:${image_tag}
          ports:
            - containerPort: ${METRICS_SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: METRICS_SERVER_PORT
              value: "${METRICS_SERVER_PORT}"
            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: AUTH_CLIENT_ID
              value: "${AUTH_CLIENT_ID}"
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: AUTH_SERVICE_USERNAME
              value: "${AUTH_SERVICE_USERNAME}"
            - name: AUTH_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-csms-io-hubacc-password

            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_CONNECTION_COLLECTION
              value: "${MONGO_CONNECTION_COLLECTION}"

            - name: NATS_URL
              value: "${NATS_URL}"
            - name: HTTP_UCC_HOST
              value: "${HTTP_UCC_HOST}"
            - name: HTTP_RETRY_MAX_INTERVAL
              value: "${HTTP_RETRY_MAX_INTERVAL}"
            - name: HTTP_LOCATION_HOST
              value: "${HTTP_LOCATION_HOST}"
            - name: HTTP_EVSE_HOST
              value: "${HTTP_EVSE_HOST}"
            - name: HTTP_TARIFF_HOST
              value: "${HTTP_TARIFF_HOST}"
            - name: HTTP_CDR_HOST
              value: "${HTTP_CDR_HOST}"
            - name: NO_OF_DAYS
              value: "${NO_OF_DAYS}"
            - name: HTTP_SESSION_HOST
              value: "${HTTP_SESSION_HOST}"
            - name: NATS_STREAM
              value: "${NATS_STREAM}"
            - name: NATS_AUTH_ENABLED
              value: "${NATS_AUTH_ENABLED}"
            - name: NATS_NKEY_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-csms-io-nats-nkey-pub
                  optional: true
            - name: NATS_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-csms-io-nats-nkey
                  optional: true
            - name: NATS_SUBJECTS
              value: "${NATS_SUBJECTS}"


