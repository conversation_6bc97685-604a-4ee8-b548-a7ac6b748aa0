resource "kubernetes_manifest" "fe_wsm_mongo_setup_job" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-mongo-setup/job.yml", {
    name                 = "fe-wsm-mongo-setup"
    image_repo           = local.image_repo
    image_tag            = local.fe-wsm-mongo-setup.version
    namespace            = "fe-wsm"

    MONGO_SCHEMA         = local.mongo_schema
    MONGO_HOST           = local.mongo_hostname
    MONGO_PORT           = local.mongo_port
    MONGO_AUTH_SOURCE    = "admin"
    MONGO_CREDENTIALS    = "fe-wsm-mongo-setup-vds"
    MONGO_OPTIONS        = "retryWrites=true&w=majority"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_config_map" "fe_wsm_mongo_setup_config" {
  metadata {
    name = "fe-wsm-mongo-setup-config"
    namespace = "fe-wsm"
  }

  data = {
    for key, config in local.collections : "${key}.yml" => yamlencode(config)
  }
}

locals {
  collections = {
    # Isolated Transaction Time Series
    isolated_transaction = {
      database    = "analytics"
      name        = "isolated_transaction"
      type        = "timeseries"
      ttl         = 1209600 # 14 Days retention
      metafield   = "meta"
      timefield   = "endTs"
      granularity = 86400 # 24-Hour Buckets
      indexes = [
        {
          name = "isTrId_1_lstUpdTs_-1"
          fields = [
            {
              name = "isTrId"
              order = 1
            },
            {
              name = "lstUpdTs"
              order = -1
            }
          ]
        },
        {
          name = "meta.siteId_1_endTs_1"
          fields = [
            {
              name = "meta.siteId"
              order = 1
            },
            {
              name = "endTs"
              order = 1
            }
          ]
        }
      ]
    }

    # Insert new collections here ...
  }
}

resource "kubernetes_manifest" "fe_wsm_mongo_setup_vds" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-mongo-setup/vds.yml", {
    name       = "fe-wsm-mongo-setup"
    namespace  = "fe-wsm"
    env        = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}

