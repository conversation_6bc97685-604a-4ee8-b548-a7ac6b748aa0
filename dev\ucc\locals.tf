locals {
  fe-ucc-version = "v1.1.1"

  fe-ucc-cli-bff = {
    "version" : "v1.1.0"
  }

  namespace = "fe-ucc"

  dp_ucc_props_api = "http://dp-ucc-props-api.dp.svc.cluster.local:8080"
  dp_ucc_telem_api = "http://dp-ucc-telem-api.dp.svc.cluster.local:8080"
  cp_ucc_cmd_api = "http://cp-ucc-cmd-api.cp.svc.cluster.local:8080"
  ia_devreg_api = "http://ia-devreg-api.ia.svc.cluster.local:8080"

  keycloak_host = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_realm = "invenco-hub"

  image_repo = "633377509572.dkr.ecr.us-east-1.amazonaws.com"
}
