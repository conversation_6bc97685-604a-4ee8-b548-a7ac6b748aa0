---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/fe-ev-opt-bff:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"

            - name: SERVER_PORT
              value: "${SERVER_PORT}"

            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"

            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"

            - name: MONGO_HOST
              value: "${MONGO_HOST}"

            - name: MONG<PERSON>_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username

            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password

            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"

            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"

            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"

            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"

            - name: MONGO_QUERY_LIMIT
              value: "${MONGO_QUERY_LIMIT}"

            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"

            - name: CONNECTIONS_COLLECTION
              value: "${CONNECTIONS_COLLECTION}"

            - name: UCC_PROPS_API
              value: "${UCC_PROPS_API}"

            - name: DEVICE_REGISTRATION_API
              value: "${DEVICE_REGISTRATION_API}"

            - name: EVSE_OPT_API
              value: "${EVSE_OPT_API}"

            - name: DEVICE_CLASS
              value: "${DEVICE_CLASS}"
            
            - name: DEVICE_TYPE
              value: "${DEVICE_TYPE}"
            
            - name: HTTP_CLIENT_TIMEOUT
              value: "${HTTP_CLIENT_TIMEOUT}"
            - name: HTTP_CLIENT_RETRY_MAX
              value: "${HTTP_CLIENT_RETRY_MAX}"

            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: AUTH_CLIENT_ID
              value: "${AUTH_CLIENT_ID}"
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: AUTH_SERVICE_USERNAME
              value: "${AUTH_SERVICE_USERNAME}"
            - name: AUTH_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-opt-bff-hubacc-password