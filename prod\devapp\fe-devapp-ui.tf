resource "kubernetes_manifest" "fe_devapp_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/devapp/fe-devapp-ui/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-devapp-ui.version

    REPLICAS = 1
    name  = "fe-devapp-ui"
    DEVAPP_DOC_URL = "https://prj-apigee-service-insight360.apigee.io/"
    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_devapp_ui" {
  metadata {
    name        = "fe-devapp-ui"
    namespace   = "fe-devapp"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-devapp-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}
