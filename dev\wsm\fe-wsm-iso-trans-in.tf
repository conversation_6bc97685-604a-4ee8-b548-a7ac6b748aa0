resource "kubernetes_config_map" "fe_wsm_iso_trans_in_schema" {
  metadata {
    name      = "fe-wsm-iso-trans-in-schema"
    namespace = "fe-wsm"
  }
  data = {
    "schema.json" = file("../../common/wsm/fe-wsm-iso-trans-in/schema.json")
  }
}

resource "kubernetes_manifest" "fe_wsm_iso_trans_in_deployment" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-in/base-deployment.yml", {
    name                  = "fe-wsm-iso-trans-in"
    namespace             = "fe-wsm"
    image_repo            = local.image_repo
    image_name            = "fe-wsm-in"
    image_tag             = local.fe-wsm-iso-trans-in.version
    REPLICAS              = 1
    COLLECTION_NAME       = "isolated_transaction"
    COLLECTION_KEY        = "isTrId"
    COLLECTION_TYPE       = "TimeSeries"
    METADATA_FIELD_NAME   = "meta"
    METADATA_FIELDS       = "compId,compNm,siteId,atgId,gvrId,siteNm,strNum,prod,pTNum,fp,mNum,lU,vU,tU,tz"
    JSON_SCHEMA_VALIDATION= "true"
    CONFIG_MAP_NAME       = kubernetes_config_map.fe_wsm_iso_trans_in_schema.metadata[0].name
    KAFKA_TOPICS          = "fe.wsm.meter.isolated_transactions"
    KAFKA_GROUP_ID        = "fe-wsm-iso-trans-in"

    EVENT_CHANNEL_SIZE = 1
    BATCH_CHANNEL_SIZE = 1
    BUFFER_SIZE        = 10
    WORKER_COUNT       = 1
    FLUSH_INTERVAL     = 10

    KAFKA_BOOTSTRAP_SERVERS = join(",", local.bootstrap_servers)
    KAFKA_MAX_BYTES         = 10000000
    KAFKA_MIN_BYTES         = 1000
    KAFKA_QUEUE_CAPACITY    = 100

    MONITORING_HOST = "0.0.0.0"
    MONITORING_PORT = 9090

    LOG_LEVEL = "debug"

    MONGO_AUTH_SOURCE   = "admin"
    MONGO_DATABASE      = local.mongo_wsm_database
    MONGO_COLLECTION    = "isolated_transaction"
    MONGO_HOST          = local.mongo_hostname
    MONGO_PORT          = local.mongo_port
    MONGO_INDEX_INIT    = "false"
    MONGO_OPTIONS       = "retryWrites=true&w=majority"
    MONGO_SCHEMA        = local.mongo_schema
    MONGO_CREDENTIALS   = "fe-wsm-iso-trans-in-vds"
    MONGO_TIMEOUT       = "30s"
    MONGO_MAX_POOL_SIZE = 100

    AWS_REGION              = "us-east-1"
  }))

  computed_fields = ["spec.template.metadata.annotations"]

  field_manager {
    force_conflicts = true
  }
  depends_on = [kubernetes_config_map.fe_wsm_iso_trans_in_schema]
}

resource "kubernetes_service" "fe_wsm_iso_trans_in" {
  metadata {
    name      = "fe-wsm-iso-trans-in"
    namespace = "fe-wsm"
    labels = {
      app = "fe-wsm-iso-trans-in"
    }
    annotations = {
      "io.cilium/global-service" : "false"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wsm-iso-trans-in"
    }
    type = "NodePort"

    port {
      name        = "metrics"
      port        = 9090
      target_port = 9090
      protocol = "TCP"
    }
  }
}

resource "kubernetes_manifest" "fe_wsm_iso_trans_in_vds" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-in/vds.yml", {
    name = "fe-wsm-iso-trans-in"
    env  = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}