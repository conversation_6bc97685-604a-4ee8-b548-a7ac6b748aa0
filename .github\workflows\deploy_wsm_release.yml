name: Deploy Front End Wet Stock Management Release
on:
  push:
    tags:
      - "release/wsm/*/v*"

jobs:
  deploy-dev:
    name: Execute Dev Deployment
    runs-on: [dev-fe]
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}

      - name: Checkout
        id: checkout
        uses: actions/checkout@v3

      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2

      - name: Execute Terraform
        id: terraform
        run: |
          cd dev/wsm
          terraform init
          terraform apply --auto-approve

  e2e-dev:
    name: Run Dev E2E Tests
    runs-on: [dev-fe]
    needs: deploy-dev
    steps:
      - name: Configure AWS Credentials
        id: config-aws
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: us-east-1

      - name: Get secrets from AWS Secrets Manager
        id: secrets
        uses: aws-actions/aws-secretsmanager-get-secrets@v1
        with:
          secret-ids: |
            dev/fe-wsm-e2e
            prod/zephyrscale
          parse-json-secrets: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: 'true'

      - name: Checkout
        id: checkout
        uses: actions/checkout@v4

      - name: Extract fe-wsm-e2e version from locals.tf dev
        id: extract-fe-wsm-e2e-version
        run: |
          echo "E2E_TAG=$(grep -A 1 'fe-wsm-e2e' dev/wsm/locals.tf | grep 'version' | sed 's/.*"version" *: *"\([^"]*\)".*/\1/')" >> $GITHUB_ENV

      - name: Pull docker image from ECR & Run e2e tests
        id: aws-ecr-pull
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: fe-wsm-e2e
          IMAGE_TAG: ${{ env.E2E_TAG }}
        run: |
          docker pull $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run \
            -e AUTH_USERNAME=$DEV_FE_WSM_E2E_AUTH_USERNAME \
            -e AUTH_CLIENT_ID=$DEV_FE_WSM_E2E_AUTH_CLIENT_ID \
            -e AUTH_CLIENT_SECRET=$DEV_FE_WSM_E2E_AUTH_CLIENT_SECRET \
            -e AUTH_PASSWORD=$DEV_FE_WSM_E2E_AUTH_PASSWORD \
            -e ZS_ACCESS_TOKEN=$PROD_ZEPHYRSCALE_ZS_ACCESS_TOKEN \
            -e ZS_PRIVATE_KEY=$PROD_ZEPHYRSCALE_ZS_PRIVATE_KEY \
            -e ZS_CONSUMER_KEY=$PROD_ZEPHYRSCALE_ZS_CONSUMER_KEY \
            $REGISTRY/$REPOSITORY:$IMAGE_TAG python main.py

  playwright-e2e-dev:
    name: Run Dev Playwright E2E Tests
    runs-on: [dev-fe]
    needs: deploy-dev
    steps:
      - name: Configure AWS Credentials
        id: config-aws
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: us-east-1

      - name: Get secrets from AWS Secrets Manager
        id: secrets
        uses: aws-actions/aws-secretsmanager-get-secrets@v1
        with:
          secret-ids: |
            dev/fe-wsm-playwright
            prod/zephyrscale
          parse-json-secrets: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: 'true'

      - name: Install AWS CLI v2
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o /tmp/awscliv2.zip
          unzip -q /tmp/awscliv2.zip -d /tmp
          rm /tmp/awscliv2.zip
          sudo /tmp/aws/install --update
          rm -rf /tmp/aws/

      - name: Checkout
        id: checkout
        uses: actions/checkout@v4

      - name: Extract fe-wsm-playwright-e2e version from locals.tf dev
        id: extract-fe-wsm-playwright-e2e-version
        run: |
          echo "UI_E2E_TAG=$(grep -A 1 'fe-wsm-playwright-e2e' dev/wsm/locals.tf | grep 'version' | sed 's/.*"version" *: *"\([^"]*\)".*/\1/')" >> $GITHUB_ENV

      - name: Pull docker image from ECR & Run Playwright tests
        id: aws-ecr-pull
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: fe-wsm-playwright
          IMAGE_TAG: ${{ env.UI_E2E_TAG }}
        run: |
          docker pull $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker run \
            -e TEST_USER_ID=$DEV_FE_WSM_PLAYWRIGHT_TEST_USER_ID \
            -e TEST_USER_PASSWORD=$DEV_FE_WSM_PLAYWRIGHT_TEST_USER_PASSWORD \
            -e JIRA_ACCESS_TOKEN=$DEV_FE_WSM_PLAYWRIGHT_JIRA_ACCESS_TOKEN \
            -e EXECUTION_PROFILE=dev \
            $REGISTRY/$REPOSITORY:$IMAGE_TAG "@status\(Automated\)"


  preview-deploy-prod:
    if: contains(github.ref, 'release/wsm/prod')
    name: Preview Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: [e2e-dev, playwright-e2e-dev]
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}

      - name: Checkout
        id: checkout
        uses: actions/checkout@v3

      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2

      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/wsm
          terraform init
          terraform plan 

  deploy-prod:
    if: contains(github.ref, 'release/wsm/prod')
    name: Execute Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: [e2e-dev, preview-deploy-prod]
    environment: prod
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}

      - name: Checkout
        id: checkout
        uses: actions/checkout@v3

      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2

      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/wsm
          terraform init
          terraform apply --auto-approve
