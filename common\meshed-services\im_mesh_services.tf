locals {
  im_server_port = 8080
}

resource "kubernetes_service" "im_alertrules_api" {
  metadata {
    name      = "im-alertrules-api"
    namespace = "im"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "im-alertrules-api"
    }
    port {
      port        = 8888
      target_port = 8888
    }
  }
}

resource "kubernetes_service" "im_alertrules_api_v2" {
  metadata {
    name      = "im-alertrules-api-v2"
    namespace = "im"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "im-alertrules-api-v2"
    }
    port {
      port        = local.im_server_port
      target_port = local.im_server_port
    }
  }
}

resource "kubernetes_service" "im_alert_api" {
  metadata {
    name      = "im-alert-api"
    namespace = "im"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "im-alert-api"
    }
    port {
      port        = local.im_server_port
      target_port = local.im_server_port
    }
  }
}
