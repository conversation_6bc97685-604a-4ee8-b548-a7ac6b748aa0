resource "kubernetes_manifest" "fe_tlstech_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/tlstech/fe-tlstech-ui/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-tlstech-ui.version

    REPLICAS   = 1
    name = "fe-tlstech-ui"

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_tlstech_ui" {
  metadata {
    name      = "fe-tlstech-ui"
    namespace = "fe-tlstech"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-tlstech-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}
