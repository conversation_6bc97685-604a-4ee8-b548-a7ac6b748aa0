locals {
  fe-tlstech-version = "v0.0.8"

  fe-tlstech-e2e = {
    "version" : "v0.0.1"
  }

  fe-tlstech-bff = {
    "version" : "v0.0.3"
  }

  fe-tlstech-ui = {
    "version" : "v0.0.6"
  }

  image_repo  = "633377509572.dkr.ecr.us-east-1.amazonaws.com"
  server_port = 8080

  kc_host_url = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  kc_realm    = "invenco-hub"

  ia_devbootstrap_api_url = "http://ia-devbootstrap-api.ia.svc.cluster.local:8080"
}
