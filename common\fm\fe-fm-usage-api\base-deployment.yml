---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-fm-usage-api
  name: fe-fm-usage-api
  namespace: fe-fm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-fm-usage-api
  template:
    metadata:
      labels:
        app: fe-fm-usage-api
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-fm-usage-api
          image: ${image_repo}/fe-fm-usage-api:${image_tag}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              value: ${MONGO_USERNAME}
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-fm-secrets
                  key: fe-fm-usage-api-atlas-password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: KC_TOKEN_URL
              value: ${KC_TOKEN_URL}
            - name: KC_CERT_URL
              value: ${KC_CERT_URL}
            - name: IA_API_URI
              value: ${IA_API_URI}
            - name: IA_CLIENT_ID
              value: ${IA_CLIENT_ID}
            - name: IA_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-fm-secrets
                  key: ia-client-secret
            - name: IA_SERVICE_USERNAME
              value: ${IA_SERVICE_USERNAME}
            - name: IA_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-fm-secrets
                  key: fe-fm-usage-api-service-password
