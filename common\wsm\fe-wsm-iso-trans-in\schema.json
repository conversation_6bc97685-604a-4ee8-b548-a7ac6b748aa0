{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"ADJ_ST_VOL": {"type": "number"}, "ATG_ID": {"type": "integer"}, "COMP_ID": {"type": "string"}, "COMP_NM": {"type": "string"}, "CRT_TS": {"type": "integer"}, "DISP_VAR": {"type": "number"}, "DISP_VAR_PC": {"type": "number"}, "END_TS": {"type": "string", "format": "date-time"}, "END_VOL_VAR": {"type": "number"}, "FP": {"type": "integer"}, "F_RT": {"type": "number"}, "GVR_ID": {"type": "string"}, "IS_TR_ID": {"type": "integer"}, "LST_UPD_TS": {"type": "integer"}, "L_U": {"type": "integer"}, "M_NUM": {"type": "integer"}, "PROD": {"type": "string"}, "P_T_NUM": {"type": "integer"}, "SITE_ID": {"type": "string"}, "SITE_NM": {"type": "string"}, "STR_NUM": {"type": "string"}, "ST_TS": {"type": "string", "format": "date-time"}, "ST_VOL_VAR": {"type": "number"}, "S_VOL": {"type": "number"}, "T_DUR": {"type": "integer"}, "T_U": {"type": "integer"}, "T_Z": {"type": "string"}, "V_U": {"type": "integer"}}, "required": ["IS_TR_ID", "ST_TS", "END_TS", "ATG_ID", "COMP_ID", "SITE_ID"]}