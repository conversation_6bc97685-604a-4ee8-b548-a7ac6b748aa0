resource "kubernetes_manifest" "fe_wm_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/wm/fe-wm-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-wm-bff.version
    REPLICAS   = 1

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL         = "info"
    SERVER_PORT       = 3000
    DP_MIRROR_API_URL = "http://dp-mirror-api-v1.dp.svc.cluster.local:8080"
    DP_TOPO_API_URL   = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"

    IA_NOTIFICATION_API_URL = "http://ia-notif-api.ia.svc.cluster.local:8080"

    CP_CMD_URL     = "http://cp-cmd-api.cp.svc.cluster.local:8081"
    CP_CMD_CLIENT  = "i360_client"
    CP_CMD_USER_ID = "<EMAIL>"

    DIGITS_FOR_OTP_VERIFICATION = "6"
    MINUTES_TTL_OTP             = "5"
    MINUTES_TTL_SESSION_TOKEN   = local.minutes_session_token_expiry
    SESSION_EMAIL_FROM          = "<EMAIL>"

    AUDIT_REPORT_PROCESS_INTERVAL_MS = "15000"
    AUDIT_REPORT_PROCESS_TIMEOUT_MS  = "300000"
    AUDIT_REPORT_EMAIL_FROM          = "<EMAIL>"

    REDIS_DB  = ""
    REDIS_URL = "redis.fe-wm.svc.cluster.local:6379"

    AUTH_DOMAIN         = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    AUTH_REALM          = "insite360-users"
    APP_DOMAIN          = "fe_wm"
    IA_CLIENT_ID        = "insite360-client"
    IA_SERVICE_USERNAME = "<EMAIL>"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_wm_bff" {
  metadata {
    name      = "fe-wm-bff"
    namespace = "fe-wm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wm-bff"
    }
    port {
      port        = 3000
      target_port = 3000
    }

    type = "NodePort"
  }
}