name: Deploy PCN Release
on:
  push:
    tags:
      - "release/pcn/*/v*"

jobs:
  deploy-dev:
    name: Execute Dev Deployment
    runs-on: [dev-fe]
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Execute Terraform
        id: terraform
        run: |
          cd dev/pcn
          terraform init
          terraform apply --auto-approve

  preview-deploy-prod:
    if: contains(github.ref, 'release/pcn/prod')
    name: Preview Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: deploy-dev
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/pcn
          terraform init
          terraform plan

  deploy-prod:
    if: contains(github.ref, 'release/pcn/prod')
    name: Execute Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: [preview-deploy-prod]
    environment: prod
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/pcn
          terraform init
          terraform apply --auto-approve
