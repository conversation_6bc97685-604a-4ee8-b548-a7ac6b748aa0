---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "${SERVER_PORT}"
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/${name}:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"
            - name: WORKER_COUNT
              value: "${WORKER_COUNT}"

            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: AUTH_CLIENT_ID
              value: "${AUTH_CLIENT_ID}"
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: AUTH_SERVICE_USERNAME
              value: "${AUTH_SERVICE_USERNAME}"
            - name: AUTH_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-obj-update-io-svc-acc-password
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTERS
              value: "${RTD_SUBJECT_FILTERS}"
            - name: HTTP_UCC_HOST
              value: "${HTTP_UCC_HOST}"
            - name: HTTP_UCC_COMMAND
              value: "${HTTP_UCC_COMMAND}"
            - name: LOCATION_ENDPOINT
              value: "${LOCATION_ENDPOINT}"
            - name: HTTP_OPT_EVSE_HOST
              value: "${HTTP_OPT_EVSE_HOST}"
            - name: RTD_AUTH_ENABLED
              value: "${RTD_AUTH_ENABLED}"
            - name: RTD_NKEY_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-obj-update-io-nats-nkey-pub
                  optional: true
            - name: RTD_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-obj-update-io-nats-nkey
                  optional: true
