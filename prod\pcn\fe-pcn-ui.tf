# TODO: parameterize more if we don't migrate to spinnaker
resource "kubernetes_manifest" "fe_pcn_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/pcn/fe-pcn-ui/base-deployment.yml", {
    name  = "fe-pcn-ui"
    NAMESPACE = local.namespace
    REPLICAS = 1

    image_repo = local.image_repo
    image_tag  = local.fe-pcn-ui.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_pcn_ui" {
  metadata {
    name        = "fe-pcn-ui"
    namespace   = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-pcn-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}
