# fe-ev-cdr-api deployment
resource "kubernetes_manifest" "fe_ev_cdr_api_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-cdr-api/base-deployment.yml", {
    name      = "fe-ev-cdr-api"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-cdr-api.version

    LOG_LEVEL       = "info"
    SERVER_PORT     = local.server_port
    SERVER_TIME_OUT = local.server_timeout

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE            = local.mongo_database_common
    MONGO_CDR_COLLECTION      = local.mongo_cdr_collection
  }))
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-cdr-api service
resource "kubernetes_service" "fe_ev_cdr_api_service" {
  metadata {
    name      = "fe-ev-cdr-api"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app   = "fe-ev-cdr-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_cdr_api_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-cdr-api/vds.yml", {
    MONGO_ROLE = "fe-ev-cdr-api"
    name       = "fe-ev-cdr-api"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_cdr_api_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-cdr-api"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
