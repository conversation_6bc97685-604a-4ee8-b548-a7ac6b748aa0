# fe-ev-session-api deployment
resource "kubernetes_manifest" "fe_ev_session_api_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-session-api/base-deployment.yml", {
    name                = "fe-ev-session-api"
    NAMESPACE           = local.namespace
    REPLICAS            = 1

    image_repo          = local.image_repo
    image_tag           = local.fe-ev-session-api.version

    MONITORING_PORT     = local.server_port
    MONITORING_HOST     = "localhost"

    LOG_LEVEL           = "info"
    SERVER_PORT         = local.server_port
    SERVER_TIMEOUT      = local.server_timeout

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE            = local.mongo_database_common
    MONGO_SESSION_COLLECTION  = local.mongo_session_collection
  }))
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-session-api service
resource "kubernetes_service" "fe_ev_session_api_service" {
  metadata {
    name      = "fe-ev-session-api"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app   = "fe-ev-session-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_session_api_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-session-api/vds.yml", {
    MONGO_ROLE = "fe-ev-session-api"
    name       = "fe-ev-session-api"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_session_api_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-session-api"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
