variable "server_port" {
  default = 8080
}

variable "app_name" {
  default = "fe-ucc-cli-bff"
}

resource "kubernetes_manifest" "fe_ucc_cli_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/ucc/fe-ucc-cli-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-ucc-cli-bff.version

    NAMESPACE = local.namespace
    NAME      = var.app_name
    REPLICAS  = 2

    LOG_LEVEL           = "info"
    SERVER_PORT         = var.server_port
    SERVER_READ_TIMEOUT = "10000"

    KC_HOST  = local.keycloak_host
    KC_REALM = local.keycloak_realm

    DP_TELEMETRY_API    = local.dp_ucc_telem_api
    DP_PROPERTIES_API   = local.dp_ucc_props_api
    CP_UCC_COMMANDS_API = local.cp_ucc_cmd_api
    IA_DEVREG_API       = local.ia_devreg_api

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ucc_cli_bff" {
  metadata {
    name      = var.app_name
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = var.app_name
    }
    port {
      port        = var.server_port
      target_port = var.server_port
    }

    type = "ClusterIP"
  }
}
