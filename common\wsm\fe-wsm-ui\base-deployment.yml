---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fe-wsm-ui
  namespace: fe-wsm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-wsm-ui
  template:
    metadata:
      labels:
        app: fe-wsm-ui
    spec:
      containers:
        - name: fe-wsm-ui
          image: ${image_repo}/fe-wsm-ui:${image_tag}
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST} 
