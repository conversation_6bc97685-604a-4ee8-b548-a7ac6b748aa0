---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fe-fm-ui-logistics
  namespace: fe-fm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-fm-ui-logistics
  template:
    metadata:
      labels:
        app: fe-fm-ui-logistics
    spec:
      containers:
        - name: fe-fm-ui-logistics
          image: ${image_repo}/fe-fm-ui-logistics:${image_tag}
          ports:
            - containerPort: 80
          env:
            - name: FE_FM_UI_BFF
              value: "fe-fm-ui-bff"
