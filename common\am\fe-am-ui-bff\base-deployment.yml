---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-am-ui-bff
  name: fe-am-ui-bff
  namespace: fe-am
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-am-ui-bff
  template:
    metadata:
      labels:
        app: fe-am-ui-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-am-ui-bff
          image: ${image_repo}/fe-am-ui-bff:${image_tag}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: DP_MIRROR_API_URL
              value: "${DP_MIRROR_API_URL}"
            - name: DP_TOPO_API_URL
              value: "${DP_TOPO_API_URL}"
            - name: KC_CERT_URL
              value: "${KC_CERT_URL}"
            - name: IM_ALERTRULES_API_URL
              value: "${IM_ALERTRULES_API_URL}"
            - name: DEVICE_CLASSES
              value: "${DEVICE_CLASSES}"
            - name: IA_API_URI
              value: "${IA_API_URI}"
            - name: FE_AM_UI_FILTERS_API_URL
              value: "${FE_AM_UI_FILTERS_API_URL}"
            - name: IM_ALERTS_API_URL
              value: "${IM_ALERTS_API_URL}"
            - name: AUTH_DOMAIN
              value: "${AUTH_DOMAIN}"
            - name: AUTH_REALM
              value: "${AUTH_REALM}"
