---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${NAME}
  name: ${NAME}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${NAME}
  template:
    metadata:
      labels:
        app: ${NAME}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${NAME}
          image: ${image_repo}/${NAME}:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            # Server
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: ":${SERVER_PORT}"
            - name: READ_HEADER_TIMEOUT
              value: ${SERVER_READ_TIMEOUT}
            # Auth
            - name: KC_HOST
              value: ${KC_HOST}
            - name: KC_REALM
              value: ${KC_REALM}
            # Proxy
            - name: TELEM_API_HOST
              value: ${DP_TELEMETRY_API}
            - name: PROPS_API_HOST
              value: ${DP_PROPERTIES_API}
            - name: CMDS_API_HOST
              value: ${CP_UCC_COMMANDS_API}
            - name: DEVREG_API_HOST
              value: ${IA_DEVREG_API}