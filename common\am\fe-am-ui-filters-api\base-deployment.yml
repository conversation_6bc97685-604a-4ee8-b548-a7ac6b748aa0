---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-am-ui-filters-api
  name: fe-am-ui-filters-api
  namespace: fe-am
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-am-ui-filters-api
  template:
    metadata:
      labels:
        app: fe-am-ui-filters-api
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-am-ui-filters-api
          image: ${image_repo}/fe-am-ui-filters-api:${image_tag}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_USERNAME
              value: "${MONGO_USERNAME}"
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-am-secrets
                  key: fe-am-ui-filters-api-atlas-password
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_FILTERES_DATABASE
              value: "${MONGO_FILTERES_DATABASE}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: IA_API_URI
              value: "${IA_API_URI}"              
            - name: KC_CERT_URL
              value: ${KC_CERT_URL}