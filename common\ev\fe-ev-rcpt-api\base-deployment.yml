---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/fe-ev-rcpt-api:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"

            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${name}-vds"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${name}-vds"
                  key: password
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"
            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"
            - name: MONGO_QUERY_LIMIT
              value: "${MONGO_QUERY_LIMIT}"
            
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_COLLECTION
              value: "${MONGO_COLLECTION}"

            - name: SERVER_TIME_OUT
              value: "${SERVER_TIME_OUT}"