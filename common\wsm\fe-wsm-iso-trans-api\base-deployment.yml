apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: "${NAMESPACE}"
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "${SERVER_PORT}"
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - image: ${image_repo}/fe-wsm-iso-trans-api:${image_tag}
          name: fe-wsm-iso-trans-api
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MON<PERSON><PERSON>_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_COLLECTION
              value: "${MONGO_COLLECTION}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"
            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: KC_HOST
              value: ${KC_HOST}
            - name: KC_REALM
              value: ${KC_REALM}
            - name: TIMEOUT
              value: ${REQUEST_TIMEOUT}