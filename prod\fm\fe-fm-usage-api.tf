resource "kubernetes_manifest" "fe_fm_usage_api_deployment" {
  manifest = yamldecode(templatefile("../../common/fm/fe-fm-usage-api/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-fm-usage-api.version
    REPLICAS   = 0

    LOG_LEVEL = "info"
    SERVER_PORT = 3000

    MONGO_AUTH_SOURCE   = "admin"
    MONGO_HOST          = "prod-fe-fm-pl-0.f3zfx.mongodb.net"
    MONGO_DATABASE      = "ifm"
    MONGO_OPTIONS       = "retryWrites=true&w=majority"
    MONGO_SCHEMA        = "mongodb+srv"
    MONGO_USERNAME      = "fe-fm-usage-api"
  
    KC_TOKEN_URL        = "https://auth.hub.invenco.com/auth/realms/insite360-users/protocol/openid-connect/token"
    KC_CERT_URL         = "http://auth.hub.invenco.com/auth/realms/insite360-users/protocol/openid-connect/certs"
    IA_API_URI          = "http://ia-api.ia.svc.cluster.local"
    IA_CLIENT_ID        = "insite360-client"
    IA_SERVICE_USERNAME = "<EMAIL>"

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_fm_usage_api" {
  metadata {
    name      = "fe-fm-usage-api"
    namespace = "fe-fm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-fm-usage-api"
    }
    port {
      port        = 3000
      target_port = 3000
    }

    type = "NodePort"
  }
}
