---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/fe-ev-e2e-twk:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: CRON_SCHEDULE
              value: "${CRON_SCHEDULE}"
            - name: TEMPORAL_HOST
              value: "${TEMPORAL_HOST}"
            - name: TEMPORAL_NAMESPACE
              value: "${TEMPORAL_NAMESPACE}"
            - name: FE_EV_CMS_API_URL
              value: "${FE_EV_CMS_API_URL}"
            - name: CP_UCC_CMD_API_URL
              value: "${CP_UCC_CMD_API_URL}"
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: HUB_AUTH_HOST
              value: "${HUB_AUTH_HOST}"
            - name: HUB_AUTH_REALM
              value: "${HUB_AUTH_REALM}"
            - name: HUB_AUTH_CLIENT_ID
              value: "${HUB_AUTH_CLIENT_ID}"
            - name: HUB_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: HUB_AUTH_USERNAME
              value: "${HUB_AUTH_USERNAME}" 
            - name: HUB_AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-e2e-twk-hubacc-password
            - name: ELVIS_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-e2e-twk-elv-us-msp-token
