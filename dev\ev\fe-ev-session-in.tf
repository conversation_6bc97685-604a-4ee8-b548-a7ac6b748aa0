# fe-ev-session-in deployment
resource "kubernetes_manifest" "fe_ev_session_in_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-session-in/base-deployment.yml", {
    name      = "fe-ev-session-in"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-session-in.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL       = "info"
    MONITOR_PORT     = local.metrics_server_port
    SERVER_TIMEOUT  = local.server_timeout


    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE            = local.mongo_database_common
    MONGO_CREDENTIALS         = local.mongo_credentials_session_in
    MONGO_SESSION_COLLECTION  = local.mongo_session_collection

    RTD_URL            = local.rtd_url
    RTD_STREAM         = local.rtd_stream
    RTD_CLIENT_NAME    = "fe-ev-session-in"
    RTD_SUBJECT_FILTER = local.rtd_session_subject_filter
    RTD_AUTH_ENABLED   = local.rtd_auth_enabled
  }))
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-session-in service
resource "kubernetes_service" "fe_ev_session_in_service" {
  metadata {
    name      = "fe-ev-session-in"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-session-in"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_session_in_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-session-in/vds.yml", {
    MONGO_ROLE = "fe-ev-session-in"
    name       = "fe-ev-session-in"
    env        = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_manifest" "fe_ev_session_in_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-session-in"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = 9090
  }))
}
