locals {
  ia_server_port     = 8080
  ia_api_server_port = 8888
}

resource "kubernetes_service" "ia_notif_api" {
  metadata {
    name      = "ia-notif-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-notif-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_ui_shell_bff" {
  metadata {
    name      = "ia-ui-shell-bff"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-ui-shell-bff"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_api" {
  metadata {
    name      = "ia-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-api"
    }
    port {
      port        = local.ia_api_server_port
      target_port = local.ia_api_server_port
    }
  }
}

resource "kubernetes_service" "ia_api_v2" {
  metadata {
    name      = "ia-api-v2"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-api-v2"
    }
    port {
      port        = local.ia_api_server_port
      target_port = local.ia_api_server_port
    }
  }
}

resource "kubernetes_service" "ia_api_v3" {
  metadata {
    name      = "ia-api-v3"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-api-v3"
    }
    port {
      port        = local.ia_api_server_port
      target_port = local.ia_api_server_port
    }
  }
}

resource "kubernetes_service" "ia_api_v4" {
  metadata {
    name      = "ia-api-v4"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-api-v4"
    }
    port {
      port        = local.ia_api_server_port
      target_port = local.ia_api_server_port
    }
  }
}

resource "kubernetes_service" "ia_company_api" {
  metadata {
    name      = "ia-company-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-company-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_location_api" {
  metadata {
    name      = "ia-location-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-location-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_site_api" {
  metadata {
    name      = "ia-site-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-site-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_contract_api" {
  metadata {
    name      = "ia-contract-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-contract-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_comm_api" {
  metadata {
    name      = "ia-comm-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-comm-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_devbootstrap_api" {
  metadata {
    name      = "ia-devbootstrap-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-devbootstrap-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_invite_api" {
  metadata {
    name      = "ia-invite-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-invite-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_nats" {
  metadata {
    name      = "nats"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      "app.kubernetes.io/component" = "nats"
      "app.kubernetes.io/instance"  = "nats"
      "app.kubernetes.io/name"      = "nats"
    }

    port {
      name        = "gateway"
      port        = 7222
      target_port = "gateway"
    }
  }
}

resource "kubernetes_service" "ia_devreg_api" {
  metadata {
    name      = "ia-devreg-api"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-devreg-api"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}

resource "kubernetes_service" "ia_contract_api_v2" {
  metadata {
    name      = "ia-contract-api-v2"
    namespace = "ia"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "false"
    }
  }
  spec {
    selector = {
      app = "ia-contract-api-v2"
    }
    port {
      port        = local.ia_server_port
      target_port = local.ia_server_port
    }
  }
}