resource "kubernetes_manifest" "fe_fm_mongo_setup_job" {
  manifest = yamldecode(templatefile("../../common/fm/fe-fm-mongo-setup/job.yml", {
    image_repo           = local.image_repo
    image_tag            = local.fe-fm-mongo-setup.version
    NAMESPACE            = "fe-fm"
    MONGO_SCHEMA         = "mongodb+srv"
    MONGO_HOST           = "prod-fe-fm-pl-0.f3zfx.mongodb.net"
    MONGO_AUTH_SOURCE    = "admin"
    MONGO_USERNAME       = "fe-fm-mongo-setup"
    MONGO_IFM_DATABASE   = "ifm"
    MONGO_OPTIONS        = "retryWrites=true&w=majority"
  }))

  field_manager {
    force_conflicts = true
  }
}
