---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-tlstech-bff
  name: fe-tlstech-bff
  namespace: fe-tlstech
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-tlstech-bff
  template:
    metadata: 
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "${SERVER_PORT}"
      labels:
        app: fe-tlstech-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-tlstech-bff
          image: ${image_repo}/fe-tlstech-bff:${image_tag}
          ports:
            - containerPort: ${SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: KC_HOST
              value: ${KC_HOST}
            - name: KC_REALM
              value: ${KC_REALM}
            - name: DEV_BOOTSTRAP_API_HOST
              value: ${DEV_BOOTSTRAP_API_HOST}
