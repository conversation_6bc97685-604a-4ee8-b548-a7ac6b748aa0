resource "kubernetes_manifest" "fe_wm_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/wm/fe-wm-ui/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-wm-ui.version
    REPLICAS   = 1
    MINUTES_EXPIRY_SESSION_TOKEN = local.minutes_session_token_expiry
    COOKIE_BANNER_TENANT_ID = "a810e717-da4c-4834-82de-a4533d8d2388"
    COOKIE_BANNER_DOMAIN_ID = "0840406b-3693-431e-9ed4-7c9bee476418"
    CONTACT_TEXT = "If you need assistance please contact the Gilbarco/Insite360 call support center at"
    CONTACT_NUMBER = "1-800-743-7501"
    MILLI_SECS_REFRESH_INTERVAL = "30000"
    ENABLE_RECAPTCHA = "1"
    RECAPTCHA_KEY = "6Lds_hMrAAAAACUE_aSFZ_IVUbG4DE04ppU1eA8L"
    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_wm_ui" {
  metadata {
    name      = "fe-wm-ui"
    namespace = "fe-wm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wm-ui"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
}

resource "kubernetes_manifest" "fe_wm_ui_ingress" {
  manifest = yamldecode(templatefile("../../common/wm/fe-wm-ui/ingress.yml", {
    host = local.fe-wm-ui.host
  }))

  field_manager {
    force_conflicts = true
  }
}
