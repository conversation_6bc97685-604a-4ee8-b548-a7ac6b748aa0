apiVersion: batch/v1
kind: Job
metadata:
  name: "${name}"
  namespace: "${NAMESPACE}"
spec:
  ttlSecondsAfterFinished: 600
  template:
    metadata:
      name: "${name}"
      namespace: "${NAMESPACE}"
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
        - name: init-delay
          image: busybox
          command: ["sleep", "15"]
      containers:
        - name: "${name}"
          image: ${image_repo}/fe-ev-csms-api:${image_tag}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_CONNECTION_COLLECTION
              value: "${MONGO_CONNECTION_COLLECTION}"
            - name: BOOTSTRAP
              value: "${BOOTSTRAP}"
            - name: SERVER_TIME_OUT
              value: "${SERVER_TIME_OUT}"
            - name: CPO_API_URL
              value: "${CPO_API_URL}"
            - name: OCPI_BASE_URL
              value: "${OCPI_BASE_URL}"
            - name: PARTY_CODE
              value: "${PARTY_CODE}"
            - name: COUNTRY_CODE
              value: "${COUNTRY_CODE}"

            - name: CREDENTIALS_TOKEN_A
              value: "${CREDENTIALS_TOKEN_A}"

            - name: AVAILABLE_VERSIONS
              value: "${AVAILABLE_VERSIONS}"
            - name: AVAILABLE_MODULES
              value: "${AVAILABLE_MODULES}"

            - name: MONGO_HOST_COMMON
              value: "${MONGO_HOST_COMMON}"
            - name: MONGO_OPTIONS_COMMON
              value: "${MONGO_OPTIONS_COMMON}"
            - name: MONGO_DATABASE_COMMON
              value: "${MONGO_DATABASE_COMMON}"
            - name: MONGO_LOCATION_COLLECTION
              value: "${MONGO_LOCATION_COLLECTION}"

            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTER
              value: "${RTD_SUBJECT_FILTER}"
            - name: RESTY_DEBUG_ENABLED
              value: "${RESTY_DEBUG_ENABLED}"
      restartPolicy: Never
