resource "kubernetes_manifest" "fe_wsm_kafka_connect_deployment" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-kafka-connect/base-deployment.yml", {
    name                      = "fe-wsm-kafka-connect"
    image_repo                = local.image_repo
    image_tag                 = local.fe-wsm-kafka-connect.version
    REPLICAS                  = 1
    CONNECT_BOOTSTRAP_SERVERS = data.aws_msk_cluster.van_int.bootstrap_brokers
    KAFKA_HEAP_OPTS           = "-Xms512M -Xmx1G"

    CPU_REQUEST               = "30m"
    MEM_REQUEST            = "1038Mi"
    MEMORY_LIMIT              = "1950Mi"

    CONNECTORS_CONFIG_PATH = "/usr/share/confluent-hub-components/connectors"
    CUSTOM_SCRIPT_PATH = "/etc/scripts/startup.sh"

    CONNECT_SASL_JAAS_CONFIG = "software.amazon.msk.auth.iam.IAMLoginModule required;"
    KAFKA_SASL_SERVER_CALLBACK_HANDLER_CLASS = "software.amazon.msk.auth.iam.IAMClientCallbackHandler"

    CONNECT_GROUP_ID = "fe-wsm-kafka-connect"

    CONNECT_CONFIG_STORAGE_TOPIC = "fe.wsm.kafkaconnect.config"
    CONNECT_OFFSET_STORAGE_TOPIC = "fe.wsm.kafkaconnect.offset"
    CONNECT_STATUS_STORAGE_TOPIC = "fe.wsm.kafkaconnect.status"
    CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR = "2"
    CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR = "2"
    CONNECT_STATUS_STORAGE_REPLICATION_FACTOR = "2"

    SNOWFLAKE_KEY_LINE_ENDING = "LF"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_wsm_kafka_connect" {
  metadata {
    name      = "fe-wsm-kafka-connect"
    namespace = "fe-wsm"
    labels = {
      app = "fe-wsm-kafka-connect"
    }
    annotations = {
      "io.cilium/global-service" : "false"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wsm-kafka-connect"
    }
    type = "NodePort"

    port {
      name        = "connector-api"
      port        = 8083
      target_port = 8083
      protocol = "TCP"
      node_port = 32083
    }

    port {
      name        = "metrics"
      port        = 5556
      target_port = 5556
      protocol = "TCP"
      node_port = 32556
    }
  }
}

resource "kubernetes_config_map" "fe-wsm-kafka-connectors-config" {
  metadata {
    name = "fe-wsm-kafka-connectors-config"
    namespace = "fe-wsm"
  }

  data = {
    for key, config in local.connectors : "${key}.json" => templatefile("../../common/wsm/fe-wsm-kafka-connect/base-connector.json", merge(config, {
      casts = join(",", formatlist("%s:float64", config.decimals))
    }))
  }
}

locals {
  snowflake_database = "IS360_DEV"
  snowflake_schema = "AVA_UAT"
  snowflake_warehouse = "IS360_AVA_WH"

  connectors = {
    # Isolated Transaction Connector
    isolated-transaction-connector = {
      account = local.snowflake_account
      database = local.snowflake_database
      warehouse = local.snowflake_warehouse
      schema = local.snowflake_schema
      user = local.snowflake_user
      interval = 300000
      connector_name = "isolated-transaction-connector"
      topic = "fe.wsm.meter.isolated_transactions"
      query = "call stream_cdc_changes('isolated_transaction_cdc_export_stream');"
      decimals = ["F_RT", "END_VOL_VAR", "ST_VOL_VAR", "ADJ_ST_VOL", "S_VOL", "DISP_VAR_PC", "DISP_VAR"]
    }

    # Add new connectors here ...
  }
}