name: Deploy Front End Developer App Release
on:
  push:
    tags:
      - "release/devapp/*/v*"

jobs:
  deploy-dev:
    name: Execute Dev Deployment
    runs-on: [dev-fe]
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2
      - name: Execute Terraform
        id: terraform
        run: |
          cd dev/devapp
          terraform init
          terraform apply --auto-approve

  # e2e-dev:
  #   name: Run Dev E2E Tests
  #   runs-on: [dev-fe]
  #   needs: deploy-dev
  #   steps:
  #     - name: Configure AWS Credentials
  #       id: config-aws
  #       uses: aws-actions/configure-aws-credentials@v2
  #       with:
  #         aws-region: us-east-1
  #     - name: Get secrets from AWS Secrets Manager
  #       id: secrets
  #       uses: aws-actions/aws-secretsmanager-get-secrets@v1
  #       with:
  #         secret-ids: |
  #           dev/fe-devapp-e2e
  #           prod/zephyrscale
  #         parse-json-secrets: true
  #     - name: Login to Amazon ECR
  #       id: login-ecr
  #       uses: aws-actions/amazon-ecr-login@v1
  #       with:
  #         mask-password: 'true'
  #     - name: Checkout
  #       id: checkout
  #       uses: actions/checkout@v4
  #     - name: Extract fe-devapp-e2e version from locals.tf dev
  #       id: extract-fe-devapp-e2e-version
  #       run: |
  #         echo "TAG=$(grep -A 1 'fe-devapp-e2e' dev/devapp/locals.tf | grep 'version' | sed 's/.*"version" *: *"\([^"]*\)".*/\1/')" >> $GITHUB_ENV
      # - name: Pull docker image from ECR & Run e2e tests
      #   id: aws-ecr-pull
      #   env:
      #     REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      #     REPOSITORY: fe-devapp-e2e
      #     IMAGE_TAG: ${{ env.TAG }}
      #   run: |
      #     docker pull $REGISTRY/$REPOSITORY:$IMAGE_TAG
      #     docker run \
      #       -e AUTH_USERNAME=$DEV_FE_WM_E2E_AUTH_USERNAME \
      #       -e AUTH_CLIENT_ID=$DEV_FE_WM_E2E_AUTH_CLIENT_ID \
      #       -e AUTH_CLIENT_SECRET=$DEV_FE_WM_E2E_AUTH_CLIENT_SECRET \
      #       -e AUTH_PASSWORD=$DEV_FE_WM_E2E_AUTH_PASSWORD \
      #       -e ZS_ACCESS_TOKEN=$PROD_ZEPHYRSCALE_ZS_ACCESS_TOKEN \
      #       -e ZS_PRIVATE_KEY=$PROD_ZEPHYRSCALE_ZS_PRIVATE_KEY \
      #       -e ZS_CONSUMER_KEY=$PROD_ZEPHYRSCALE_ZS_CONSUMER_KEY \
      #       $REGISTRY/$REPOSITORY:$IMAGE_TAG python main.py

  preview-deploy-prod:
    if: contains(github.ref, 'release/devapp/prod')
    name: Preview Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: deploy-dev
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2
      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/devapp
          terraform init
          terraform plan

  deploy-prod:
    if: contains(github.ref, 'release/devapp/prod')
    name: Execute Prod Deployment
    runs-on: [prod-fe-fe-deploy]
    needs: [preview-deploy-prod]
    environment: prod
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_INFRACOM_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v3
      - name: Setup Node # prereq for terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v2
      - name: Execute Terraform
        id: terraform
        run: |
          cd prod/devapp
          terraform init
          terraform apply --auto-approve
