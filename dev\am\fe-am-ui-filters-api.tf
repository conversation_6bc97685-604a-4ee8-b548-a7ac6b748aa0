resource "kubernetes_manifest" "fe_am_ui_filters_api_deployment" {
  manifest = yamldecode(templatefile("../../common/am/fe-am-ui-filters-api/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-am-ui-filters-api.version
    REPLICAS   = 1
    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL = "info"
    SERVER_PORT = 8080

    MONGO_AUTH_SOURCE = "admin"
    MONGO_HOST = "dev-fe-am-pl-0.fvib5.mongodb.net"
    MONGO_FILTERES_DATABASE = "am"
    MONGO_OPTIONS = "retryWrites=true&w=majority"
    MONGO_SCHEMA = "mongodb+srv"
    MONGO_USERNAME = "fe-am-ui-filters-api"
    MONGO_PORT = 27017
    KC_CERT_URL = "https://auth.hub-dev.invenco.com/auth/realms/insite360-users/protocol/openid-connect/certs"
    IA_API_URI = "http://ia-api-v2.ia.svc.cluster.local:8888"
    
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_am_ui_filters_api" {
  metadata {
    name      = "fe-am-ui-filters-api"
    namespace = "fe-am"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-am-ui-filters-api"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    
  }
}