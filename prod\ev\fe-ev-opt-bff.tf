# fe-ev-opt-bff deployment
resource "kubernetes_manifest" "fe_ev_opt_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-opt-bff/base-deployment.yml", {
    name      = "fe-ev-opt-bff"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-opt-bff.version

    LOG_LEVEL           = "info"
    SERVER_PORT         = local.server_port
    SERVER_READ_TIMEOUT = local.server_timeout

    HTTP_CLIENT_TIMEOUT      = "30s"
    HTTP_CLIENT_RETRY_MAX    = "3"

    MONGO_HOST                  = local.mongo_hostname_secure
    MONGO_PORT                  = local.mongo_port
    MONGO_CREDENTIALS           = local.mongo_credentials_opt_bff
    MONGO_AUTH_SOURCE           = local.mongo_auth_source
    MONGO_DATABASE              = local.mongo_database_secure
    CONNECTIONS_COLLECTION      = local.mongo_connection_collection
    MONGO_OPTIONS               = local.mongo_options_secure
    MONGO_SCHEMA                = local.mongo_schema
    MONGO_MAX_POOL_SIZE         = "100"
    MONGO_TIMEOUT               = "30s"
    MONGO_QUERY_LIMIT           = "25"

    EVSE_OPT_API            = local.opt_evse_url
    DEVICE_REGISTRATION_API = local.dev_reg_url
    UCC_PROPS_API           = local.ucc_props_url

    DEVICE_CLASS            = local.device_class_filter
    DEVICE_TYPE             = local.device_type_filter
    
    KC_HOST               = local.keycloak_host
    KC_REALM              = local.keycloak_invenco_realm
    AUTH_CLIENT_ID        = local.invenco_client_id
    AUTH_SERVICE_USERNAME = "<EMAIL>"

  }))

  computed_fields = ["spec.template.meta1.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-opt-bff service
resource "kubernetes_service" "fe_ev_opt_bff_service" {
  metadata {
    name      = "fe-ev-opt-bff"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-opt-bff"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "ClusterIP"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_opt_bff_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-opt-bff/vds.yml", {
    MONGO_ROLE = "fe-ev-opt-bff"
    name       = "fe-ev-opt-bff"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_opt_bff_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-opt-bff"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
