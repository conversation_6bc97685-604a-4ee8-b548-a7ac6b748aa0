---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${name}
  namespace: ${namespace}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      labels:
        app: ${name}
    spec:
      containers:
        - name: ${name}
          image: ${image_repo}/${image_name}:${image_tag}
          env:
            - name: COLLECTION_NAME
              value: "${COLLECTION_NAME}"
            - name: COLLECTION_TYPE
              value: "${COLLECTION_TYPE}"
            - name: COLLECTION_KEY
              value: "${COLLECTION_KEY}"
            - name: METADATA_FIELDS
              value: "${METADATA_FIELDS}"
            - name: METADATA_FIELD_NAME
              value: "${METADATA_FIELD_NAME}"
            - name: JSON_SCHEMA_VALIDATION
              value: "${JSON_SCHEMA_VALIDATION}"
            - name: EVENT_CHANNEL_SIZE
              value: "${EVENT_CHANNEL_SIZE}"
            - name: BATCH_CHANNEL_SIZE
              value: "${BATCH_CHANNEL_SIZE}"
            - name: BUFFER_SIZE
              value: "${BUFFER_SIZE}"
            - name: WORKER_COUNT
              value: "${WORKER_COUNT}"
            - name: FLUSH_INTERVAL
              value: "${FLUSH_INTERVAL}"
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "${KAFKA_BOOTSTRAP_SERVERS}"
            - name: KAFKA_GROUP_ID
              value: "${KAFKA_GROUP_ID}"
            - name: KAFKA_MAX_BYTES
              value: "${KAFKA_MAX_BYTES}"
            - name: KAFKA_MIN_BYTES
              value: "${KAFKA_MIN_BYTES}"
            - name: KAFKA_TOPICS
              value: "${KAFKA_TOPICS}"
            - name: KAFKA_QUEUE_CAPACITY
              value: "${KAFKA_QUEUE_CAPACITY}"
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_TIMEOUT
              value: "${MONGO_TIMEOUT}"
            - name: MONGO_MAX_POOL_SIZE
              value: "${MONGO_MAX_POOL_SIZE}"
            - name: MONITORING_HOST
              value: "${MONITORING_HOST}"
            - name: MONITORING_PORT
              value: "${MONITORING_PORT}"
            - name: AWS_REGION
              value: "${AWS_REGION}"
          volumeMounts:
            - name: schema-config
              mountPath: /config
              readOnly: true
      volumes:
        - name: schema-config
          configMap:
            name: ${CONFIG_MAP_NAME} 