locals {
  fe-wsm-version = "v0.5.0"

  fe-wsm-bff = {
    "version" : "v0.4.10"
  }


  fe-wsm-iso-trans-api = {
    "version" : "v0.0.2"
  }

  fe-wsm-ui = {
    "version" : "v0.3.17"
  }

  fe-wsm-e2e = {
    "version" : "v1.0.3"
  }

  fe-wsm-playwright-e2e = {
    "version" : "v1.0.24"
  }

  fe-wsm-mongo-setup = {
    version = "v1.0.0"
  }

  fe-wsm-kafka-connect = {
    version = "v1.1.0"
  }

  fe-wsm-iso-trans-in = {
    "version": "v1.0.0"
  }

  keycloak_host = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_realm = "invenco-hub"
  keycloak_invenco_realm =  local.keycloak_realm

  topo_api_url = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"
  appreg_api_url = "http://ia-appreg-api.ia.svc.cluster.local:8080"

  image_repo = "************.dkr.ecr.us-east-1.amazonaws.com"

  mongo_hostname = "ava-pl-0.iq83ct.mongodb.net"
  mongo_port = 27017
  mongo_schema = "mongodb+srv"
  mongo_wsm_database = "analytics"

  snowflake_user    = "IS360_DATA_PLANE_SVC"
  snowflake_account = "aws_gvr_flk.us-east-1"

  van_int_msk = "dev-van-int"

  bootstrap_servers = [
    "b-1.devvanint.ae97pq.c12.kafka.us-east-1.amazonaws.com:9092",
    "b-2.devvanint.ae97pq.c12.kafka.us-east-1.amazonaws.com:9092",
    "b-3.devvanint.ae97pq.c12.kafka.us-east-1.amazonaws.com:9092"
  ]

  server_port = 8080

}

data "aws_msk_cluster" "van_int" {
  cluster_name = local.van_int_msk
}
