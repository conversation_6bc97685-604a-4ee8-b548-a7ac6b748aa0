apiVersion: batch/v1
kind: Job
metadata:
  name: fe-fm-mongo-setup
  namespace: "${NAMESPACE}"
spec:
  ttlSecondsAfterFinished: 60
  template:
    metadata:
      name: fe-fm-mongo-setup
      namespace: fe-fm
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - image: ${image_repo}/fe-fm-mongo-setup:${image_tag}
          name: fe-fm-mongo-setup
          env:
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_USERNAME
              value: "${MONGO_USERNAME}"
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-secrets
                  key: fe-fm-mongo-setup-atlas-password
            - name: MON<PERSON><PERSON>_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_IFM_DATABASE
              value: "${MONGO_IFM_DATABASE}"
      restartP<PERSON>y: Never
