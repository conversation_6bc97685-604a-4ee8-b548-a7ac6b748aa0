resource "kubernetes_manifest" "fe_ev_finadv_io_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-finadv-io/base-deployment.yml", {
    name      = "fe-ev-finadv-io"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-finadv-io.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL                   = "info"
    SERVER_READ_TIMEOUT         = local.server_timeout
    METRICS_SERVER_PORT         = local.metrics_server_port

    MONGO_HOST                  = local.mongo_hostname_common
    MONGO_PORT                  = local.mongo_port
    MONGO_CREDENTIALS           = local.mongo_credentials_finadv_io
    MONGO_AUTH_SOURCE           = local.mongo_auth_source
    MONGO_DATABASE              = local.mongo_database_common
    MONGO_COLLECTION            = "receipts"
    MONGO_OPTIONS               = local.mongo_options_common
    MONGO_SCHEMA                = local.mongo_schema

    RTD_ENABLED                 = true
    RTD_URL                     = local.rtd_url
    RTD_STREAM                  = "dp-ucc-telem-in"
    RTD_SUBJECT_FILTER          = "dp-ucc-telem-in.eds-finadv.>"
    RTD_CLIENT_NAME             = "fe-ev-finadv-io"
    RTD_AUTH_ENABLED            = false
    TEMPLATE_URL                = local.template_url
    OPT_EVSE_URL                = local.opt_evse_url
    UCC_URL                     = local.ucc_cmd_url

    KC_HOST               = local.keycloak_host
    KC_REALM              = local.keycloak_invenco_realm
    AUTH_CLIENT_ID        = local.invenco_client_id
    AUTH_SERVICE_USERNAME = "<EMAIL>"

  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_finadv_io_service" {
  metadata {
    name      = "fe-ev-finadv-io"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-finadv-io"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}


#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_finadv_io_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-finadv-io/vds.yml", {
    MONGO_ROLE = "fe-ev-finadv-io"
    name       = "fe-ev-finadv-io"
    env        = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}
