# fe-ev-location-in deployment
resource "kubernetes_manifest" "fe_ev_location_in_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-location-in/base-deployment.yml", {
    name      = "fe-ev-location-in"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-location-in.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL       = "info"
    MONITORING_PORT     = local.metrics_server_port
    SERVER_TIME_OUT = local.server_timeout


    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE            = local.mongo_database_common
    MONGO_CREDENTIALS         = local.mongo_credentials_location_in
    MONGO_LOCATION_COLLECTION = local.mongo_location_collection

    RTD_URL            = local.rtd_url
    RTD_STREAM         = local.rtd_stream
    RTD_CLIENT_NAME    = "fe-ev-location-in"
    RTD_SUBJECT_FILTER = local.rtd_location_subject_filter
    RTD_AUTH_ENABLED   = local.rtd_auth_enabled
  }))
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-location-in service
resource "kubernetes_service" "fe_ev_location_in_service" {
  metadata {
    name      = "fe-ev-location-in"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-location-in"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_location_in_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-location-in/vds.yml", {
    MONGO_ROLE = "fe-ev-location-in"
    name       = "fe-ev-location-in"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_manifest" "fe_ev_location_in_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-location-in"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = 9090
  }))
}
