variable "app_name" {
  default = "fe-ppe-status-bff"
}

resource "kubernetes_manifest" "fe_ppe_status_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/ppe/fe-ppe-status-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-ppe-status-bff.version

    NAMESPACE = local.namespace
    NAME      = var.app_name
    REPLICAS  = 0

    LOG_LEVEL             = "info"
    SERVER_PORT           = local.bff_server_port
    SERVER_READ_TIMEOUT   = local.server_read_timeout
    HTTP_CLIENT_TIMEOUT   = local.http_client_timeout
    HTTP_CLIENT_RETRY_MAX = local.http_client_retry_max

    KC_HOST  = local.keycloak_host
    KC_REALM = local.keycloak_invenco_realm

    LOGS_API_URL            = local.fe_ppe_logs_api_url
    RSU_API_URL             = local.rsu_api_url
    PPE_DEPLOYMENTS_API_URL = local.cp_ppe_deployments_api_url
    UCC_PROPS_API_URL       = local.dp_ucc_props_api_url
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ppe_status_bff_service" {
  metadata {
    name      = var.app_name
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = var.app_name
    }
    port {
      port        = local.bff_server_port
      target_port = local.bff_server_port
    }

    type = "ClusterIP"
  }
}


# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ppe_status_bff_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ppe-status-bff"
    NAMESPACE   = local.namespace
    TARGET_PORT = local.podmonitor_port
  }))
}
