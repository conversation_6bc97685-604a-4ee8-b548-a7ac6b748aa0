---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-fm-ui-bff
  name: fe-fm-ui-bff
  namespace: fe-fm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-fm-ui-bff
  template:
    metadata:
      labels:
        app: fe-fm-ui-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-fm-ui-bff
          image: ${image_repo}/fe-fm-ui-bff:${image_tag}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: DP_MIRROR_API_URL
              value: "${DP_MIRROR_API_URL}"
            - name: DP_TOPO_API_URL
              value: "${DP_TOPO_API_URL}"
            - name: ALERT_RULES_API_URL
              value: "${ALERT_RULES_API_URL}"
            - name: DP_CDC_API_URL
              value: "${DP_CDC_API_URL}"