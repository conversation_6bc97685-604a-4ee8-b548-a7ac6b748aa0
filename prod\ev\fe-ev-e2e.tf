resource "kubernetes_manifest" "fe_ev_e2e_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-e2e/base-deployment.yml", {
    name      = "fe-ev-e2e"
    NAMESPACE = local.namespace
    REPLICAS  = 0

    image_repo = local.image_repo
    image_tag  = local.fe-ev-e2e.version

    LOG_LEVEL                   = "info"
    SERVER_PORT                 = local.server_port
    SERVER_READ_TIMEOUT         = local.server_timeout
    METRICS_SERVER_PORT         = local.metrics_server_port

    RTD_ENABLED                 = true
    RTD_URL                     = local.rtd_url
    RTD_STREAM                  = local.rtd_stream
    RTD_SUBJECT_FILTER          = "dp-ucc-telem-in.eds-ev.ocpiCommand.>"
    RTD_CLIENT_NAME             = "fe-ev-e2e"

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_e2e_service" {
  metadata {
    name      = "fe-ev-e2e"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-e2e"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}
