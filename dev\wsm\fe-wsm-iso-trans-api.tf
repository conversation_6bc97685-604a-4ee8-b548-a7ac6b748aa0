resource "kubernetes_manifest" "fe_wsm_iso_trans_api_deployment" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-iso-trans-api/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-wsm-iso-trans-api.version
    name       = "fe-wsm-iso-trans-api"
    REPLICAS   = 1
    NAMESPACE  = "fe-wsm"
    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
    LOG_LEVEL  = "debug"

    MONGO_AUTH_SOURCE   = "admin"
    MONGO_DATABASE      = local.mongo_wsm_database
    MONGO_COLLECTION    = "isolated_transaction"
    MONGO_HOST          = local.mongo_hostname
    MONGO_PORT          = local.mongo_port
    MONGO_INDEX_INIT    = "false"
    MONGO_OPTIONS       = "retryWrites=true&w=majority"
    MONGO_SCHEMA        = local.mongo_schema
    MONGO_CREDENTIALS   = "fe-wsm-iso-trans-api-vds"
    MONGO_TIMEOUT       = "30s"
    MONGO_MAX_POOL_SIZE = 100

    SERVER_PORT = local.server_port

    KC_HOST         = local.keycloak_host
    KC_REALM        = local.keycloak_invenco_realm
    REQUEST_TIMEOUT = "30s"
  }))

  computed_fields = ["spec.template.metadata.annotations"]

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe-wsm-iso-trans-api" {
  metadata {
    name      = "fe-wsm-iso-trans-api"
    namespace = "fe-wsm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wsm-iso-trans-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }

    type = "NodePort"
  }
}

resource "kubernetes_manifest" "fe_wsm_iso_trans_api_vds" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-iso-trans-api/vds.yml", {
    name = "fe-wsm-iso-trans-api"
    env  = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}