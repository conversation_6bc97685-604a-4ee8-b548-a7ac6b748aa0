{"connection.warehouse": "${warehouse}", "connector.class": "io.confluent.connect.jdbc.JdbcSourceConnector", "connection.database": "${database}", "tasks.max": "1", "connection.schema": "${schema}", "query": "${query}", "transforms": "<PERSON><PERSON><PERSON><PERSON>", "mode": "bulk", "poll.interval.ms": "${interval}", "connection.private_key_file": "/usr/share/confluent-hub-components/rsa_key.p8", "topic.prefix": "${topic}", "transforms.castValues.spec": "${casts}", "transforms.castValues.type": "org.apache.kafka.connect.transforms.Cast$Value", "connection.user": "${user}", "name": "${connector_name}", "value.converter.schemas.enable": "false", "numeric.mapping": "best_fit", "auto.create": "false", "connection.url": "jdbc:snowflake://${account}.snowflakecomputing.com", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "connection.authenticator": "snowflake_jwt", "key.converter": "org.apache.kafka.connect.json.JsonConverter"}