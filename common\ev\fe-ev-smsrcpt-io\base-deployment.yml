---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: ${REPLICAS}
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/${name}:${image_tag}
          ports:
            - containerPort: ${METRICS_SERVER_PORT}
          resources:
            requests:
              cpu: "${CPU_REQUEST}"
              memory: "${MEM_REQUEST}"
          volumeMounts:
            - name: translations-config
              mountPath: /app/config
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"
            - name: METRICS_SERVER_PORT
              value: "${METRICS_SERVER_PORT}"
            - name: WORKER_COUNT
              value: "${WORKER_COUNT}"
            
            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: AUTH_CLIENT_ID
              value: "${AUTH_CLIENT_ID}"
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: AUTH_SERVICE_USERNAME
              value: "${AUTH_SERVICE_USERNAME}"
            - name: AUTH_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-smsrcpt-io-hubacc-password

            - name: RTD_AUTH_ENABLED
              value: "${RTD_AUTH_ENABLED}"
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTERS
              value: "${RTD_SUBJECT_FILTERS}"
            - name: RTD_RETRY_ON_FAILED_CONNECT
              value: "${RTD_RETRY_ON_FAILED_CONNECT}"
            - name: RTD_MAX_RECONNECTS
              value: "${RTD_MAX_RECONNECTS}"
            - name: RTD_BUFFER
              value: "${RTD_BUFFER}"
            - name: RTD_NKEY_PUBLIC
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-smsrcpt-io-nkey-pub
                  optional: true
            - name: RTD_NKEY_PRIVATE
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-smsrcpt-io-nkey
                  optional: true
            
            - name: IA_NOTIF_API_URL
              value: "${IA_NOTIF_API_URL}"
            - name: HTTP_CLIENT_TIMEOUT
              value: "${HTTP_CLIENT_TIMEOUT}"
            - name: HTTP_CLIENT_RETRY_MAX
              value: "${HTTP_CLIENT_RETRY_MAX}"
            
            - name: TRANSLATIONS_CONFIG_PATH
              value: "${TRANSLATIONS_CONFIG_PATH}"
            - name: DEFAULT_TEXT_MESSAGE_TEMPLATE
              value: "${DEFAULT_TEXT_MESSAGE_TEMPLATE}"
            - name: DEFAULT_LANGUAGE_CODE
              value: "${DEFAULT_LANGUAGE_CODE}"
            - name: DEFAULT_MESSAGE
              value: "${DEFAULT_MESSAGE}"
            - name: RELOAD_AFTER
              value: "${RELOAD_AFTER}"
            - name: NOTIFY_UPDATE_EVERY
              value: "${NOTIFY_UPDATE_EVERY}"
      volumes:
        - name: translations-config
          configMap:
            name: fe-ev-smsrcpt-io-config
